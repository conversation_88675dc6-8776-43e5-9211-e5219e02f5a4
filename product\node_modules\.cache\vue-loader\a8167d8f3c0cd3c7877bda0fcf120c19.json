{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\BarScrollChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\BarScrollChart.vue", "mtime": 1755589243987}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["BarScrollChart.vue"], "names": [], "mappings": ";AAKA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BarScrollChart.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\r\n  <div :id=\"id\" class=\"bar-scroll-chart\"></div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\n\r\nexport default {\r\n  name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\r\n  props: {\r\n    id: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    showCount: {\r\n      type: Number,\r\n      required: true\r\n    },\r\n    chartData: {\r\n      type: Array,\r\n      required: true,\r\n      default: () => []\r\n    }\r\n  },\r\n  data () {\r\n    return {\r\n      chart: null,\r\n      timer: null,\r\n      currentIndex: 0\r\n      // showCount: 5 // 一屏显示5条\r\n    }\r\n  },\r\n  mounted () {\r\n    this.initChart()\r\n    this.startScroll()\r\n  },\r\n  beforeDestroy () {\r\n    if (this.chart) this.chart.dispose()\r\n    if (this.timer) clearInterval(this.timer)\r\n  },\r\n  methods: {\r\n    getBarColor () {\r\n      return {\r\n        type: 'linear',\r\n        x: 0,\r\n        y: 0,\r\n        x2: 1,\r\n        y2: 0,\r\n        colorStops: [\r\n          { offset: 0, color: '#062553' },\r\n          { offset: 1, color: 'rgba(31, 198, 255, 1)' }\r\n        ]\r\n      }\r\n    },\r\n    getBgBarColor () {\r\n      return 'rgba(35,225,255,0.08)'\r\n    },\r\n    getOption (data) {\r\n      return {\r\n        grid: {\r\n          left: this.id === 'committee-statistics' ? 50 : 70,\r\n          right: 10,\r\n          top: 15,\r\n          bottom: 10,\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'value',\r\n          min: 0,\r\n          max: Math.max(...this.chartData.map(d => d.value)) * 1.1,\r\n          splitLine: { show: false },\r\n          axisLine: { show: false },\r\n          axisTick: { show: false },\r\n          axisLabel: { show: false }\r\n        },\r\n        yAxis: [\r\n          {\r\n            // 序号轴 - 固定在最左侧\r\n            type: 'category',\r\n            inverse: true,\r\n            data: data.map((_, idx) => {\r\n              const num = ((this.currentIndex + idx) % this.chartData.length) + 1\r\n              return num.toString().padStart(2, '0')\r\n            }),\r\n            axisTick: { show: false },\r\n            axisLine: { show: false },\r\n            axisLabel: {\r\n              show: true,\r\n              color: 'rgba(255, 255, 255, 0.5)',\r\n              fontSize: 13,\r\n              fontFamily: 'DIN',\r\n              fontWeight: '500',\r\n              align: 'center',\r\n              margin: 8\r\n            },\r\n            position: 'left',\r\n            offset: -50\r\n          },\r\n          {\r\n            // 名称轴\r\n            type: 'category',\r\n            inverse: true,\r\n            data: data.map(item => item.name),\r\n            axisTick: { show: false },\r\n            axisLine: { show: false },\r\n            axisLabel: {\r\n              show: true,\r\n              color: '#fff',\r\n              fontSize: 15,\r\n              align: 'left',\r\n              margin: 16\r\n            }\r\n          },\r\n          {\r\n            // 右侧数值\r\n            type: 'category',\r\n            inverse: true,\r\n            data: data.map(item => item.value),\r\n            axisTick: { show: false },\r\n            axisLine: { show: false },\r\n            axisLabel: {\r\n              show: true,\r\n              color: '#A0F6FF',\r\n              fontSize: 18,\r\n              align: 'left',\r\n              margin: 12\r\n            }\r\n          }\r\n        ],\r\n        series: [\r\n          {\r\n            type: 'bar',\r\n            barWidth: 6,\r\n            yAxisIndex: 1, // 使用名称轴\r\n            data: data.map(item => item.value),\r\n            itemStyle: {\r\n              color: this.getBarColor(),\r\n              borderRadius: 6\r\n            },\r\n            z: 2\r\n          },\r\n          {\r\n            // 背景条\r\n            type: 'bar',\r\n            barWidth: 8,\r\n            yAxisIndex: 1, // 使用名称轴\r\n            data: data.map(() => Math.max(...this.chartData.map(d => d.value)) * 1.1),\r\n            itemStyle: {\r\n              color: this.getBgBarColor(),\r\n              borderRadius: 6\r\n            },\r\n            barGap: '-100%',\r\n            z: 1\r\n          }\r\n        ]\r\n      }\r\n    },\r\n    initChart () {\r\n      const chartContainer = document.getElementById(this.id)\r\n      if (!chartContainer) return\r\n      this.chart = echarts.init(chartContainer)\r\n      this.renderChart()\r\n      window.addEventListener('resize', this.resizeChart)\r\n    },\r\n    renderChart () {\r\n      // 滚动窗口数据\r\n      let data = []\r\n      if (this.chartData.length <= this.showCount) {\r\n        data = this.chartData\r\n      } else {\r\n        // 实现无缝滚动\r\n        const start = this.currentIndex\r\n        const end = start + this.showCount\r\n        if (end <= this.chartData.length) {\r\n          data = this.chartData.slice(start, end)\r\n        } else {\r\n          data = this.chartData.slice(start).concat(this.chartData.slice(0, end - this.chartData.length))\r\n        }\r\n      }\r\n      // 保持原顺序，新数据在最下方\r\n      this.chart.setOption(this.getOption(data), true)\r\n    },\r\n    startScroll () {\r\n      if (this.timer) clearInterval(this.timer)\r\n      this.timer = setInterval(() => {\r\n        if (this.chartData.length <= this.showCount) return\r\n        this.currentIndex = (this.currentIndex + 1) % this.chartData.length\r\n        this.renderChart()\r\n      }, 3000)\r\n    },\r\n    resizeChart () {\r\n      if (this.chart) this.chart.resize()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.bar-scroll-chart {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n</style>\r\n"]}]}