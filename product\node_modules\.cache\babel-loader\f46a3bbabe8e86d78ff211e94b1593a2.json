{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\committeeStatistics\\committeeStatisticsBox.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\committeeStatistics\\committeeStatisticsBox.vue", "mtime": 1755588969817}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AA6IA;AACA;AAEA;EACAA,iBADA;EAEAC;IACAC;EADA,CAFA;;EAKAC;IACA;MACAC,eADA;MAEA;MACAC,gBACA;QAAAL;QAAAM;QAAAC;MAAA,CADA,EAEA;QAAAP;QAAAM;QAAAC;MAAA,CAFA,EAGA;QAAAP;QAAAM;QAAAC;MAAA,CAHA,EAIA;QAAAP;QAAAM;QAAAC;MAAA,CAJA,EAKA;QAAAP;QAAAM;QAAAC;MAAA,CALA,EAMA;QAAAP;QAAAM;QAAAC;MAAA,CANA,CAHA;MAWA;MACAC,YACA;QAAAR;QAAAM;QAAAC;QAAAE;MAAA,CADA,EAEA;QAAAT;QAAAM;QAAAC;QAAAE;MAAA,CAFA,EAGA;QAAAT;QAAAM;QAAAC;QAAAE;MAAA,CAHA,EAIA;QAAAT;QAAAM;QAAAC;QAAAE;MAAA,CAJA,EAKA;QAAAT;QAAAM;QAAAC;QAAAE;MAAA,CALA,EAMA;QAAAT;QAAAM;QAAAC;QAAAE;MAAA,CANA,EAOA;QAAAT;QAAAM;QAAAC;QAAAE;MAAA,CAPA,EAQA;QAAAT;QAAAM;QAAAC;QAAAE;MAAA,CARA,EASA;QAAAT;QAAAM;QAAAC;QAAAE;MAAA,CATA,EAUA;QAAAT;QAAAM;QAAAC;QAAAE;MAAA,CAVA,CAZA;MAwBA;MACAC,qBACA;QAAAV;QAAAM;MAAA,CADA,EAEA;QAAAN;QAAAM;MAAA,CAFA,EAGA;QAAAN;QAAAM;MAAA,CAHA,EAIA;QAAAN;QAAAM;MAAA,CAJA,EAKA;QAAAN;QAAAM;MAAA,CALA,EAMA;QAAAN;QAAAM;MAAA,CANA,EAOA;QAAAN;QAAAM;MAAA,CAPA,EAQA;QAAAN;QAAAM;MAAA,CARA,EASA;QAAAN;QAAAM;MAAA,CATA,EAUA;QAAAN;QAAAM;MAAA,CAVA,EAWA;QAAAN;QAAAM;MAAA,CAXA,EAYA;QAAAN;QAAAM;MAAA,CAZA,EAaA;QAAAN;QAAAM;MAAA,CAbA,EAcA;QAAAN;QAAAM;MAAA,CAdA,EAeA;QAAAN;QAAAM;MAAA,CAfA,EAgBA;QAAAN;QAAAM;MAAA,CAhBA,EAiBA;QAAAN;QAAAM;MAAA,CAjBA,EAkBA;QAAAN;QAAAM;MAAA,CAlBA,EAmBA;QAAAN;QAAAM;MAAA,CAnBA,EAoBA;QAAAN;QAAAM;MAAA,CApBA,EAqBA;QAAAN;QAAAM;MAAA,CArBA;IAzBA;EAiDA,CAvDA;;EAwDAK,YAxDA;;EA0DAC;IACA;IACA;IACA;EACA,CA9DA;;EA+DAC;IACA;MACAC;IACA;EACA,CAnEA;;EAoEAC;IACAC;MACA;QAAAC;QAAAC;MAAA;MACAD;MACAC;IACA,CALA;;IAMAC;MACA;MACA;QACAC,eADA;QAEAC,gBAFA;QAGAC,cAHA;QAIAC,eAJA;QAKAC,iBALA;QAMAC;MANA;IAQA,CAhBA;;IAiBA;IACAC;MACA;QAAAC;MAAA;IACA;;EApBA;AApEA", "names": ["name", "components", "BarScrollChart", "data", "currentTime", "educationData", "value", "percentage", "partyData", "color", "sectorAnalysisData", "computed", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "initScreen", "calcRate", "windowDraw", "updateTime", "year", "month", "day", "hour", "minute", "second", "goHome", "path"], "sourceRoot": "src/views/smartBrainLargeScreen/committeeStatistics", "sources": ["committeeStatisticsBox.vue"], "sourcesContent": ["<template>\r\n  <div class=\"big-screen\" ref=\"bigScreen\">\r\n    <div class=\"screen-header\">\r\n      <div class=\"header-left\">\r\n        <span class=\"date-time\">{{ currentTime }}</span>\r\n        <span class=\"weather\">晴 24℃ 东南风</span>\r\n      </div>\r\n      <div class=\"header-center\">\r\n        <img src=\"../../../assets/largeScreen/top_header_txt.png\" alt=\"\" style=\"height: 50px;\">\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <div class=\"header-buttons\">\r\n          <div class=\"header-btn area-select-btn\">\r\n            <span>选择地区</span>\r\n          </div>\r\n          <div class=\"header-btn current-module-btn\">\r\n            <span>委员统计</span>\r\n          </div>\r\n          <div class=\"header-btn home-btn\" @click=\"goHome\">\r\n            <span>返回首页</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"screen-content\">\r\n      <div class=\"left-panel\">\r\n        <!-- 委员数量 -->\r\n        <div class=\"committee-count-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">委员数量</span>\r\n          </div>\r\n          <div class=\"count-content\">\r\n            <div class=\"count-item\">\r\n              <div class=\"count-value\">418</div>\r\n              <div class=\"count-label\">委员总数</div>\r\n            </div>\r\n            <div class=\"count-item\">\r\n              <div class=\"count-value\">42</div>\r\n              <div class=\"count-label\">政协常委</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 性别比例 -->\r\n        <div class=\"gender-ratio-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">性别比例</span>\r\n          </div>\r\n          <div class=\"gender-content\">\r\n            <div class=\"gender-chart\">\r\n              <div class=\"ratio-item\">\r\n                <div class=\"ratio-circle male\">70%</div>\r\n              </div>\r\n              <div class=\"ratio-item\">\r\n                <div class=\"ratio-circle female\">30%</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 年龄 -->\r\n        <div class=\"age-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">年龄</span>\r\n          </div>\r\n          <div class=\"age-content\">\r\n            <div class=\"age-pie-chart\">\r\n              <!-- 年龄饼图占位 -->\r\n              <div class=\"age-chart-placeholder\">年龄分布图</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 学历 -->\r\n        <div class=\"education-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">学历</span>\r\n          </div>\r\n          <div class=\"education-content\">\r\n            <div class=\"education-bars\">\r\n              <div v-for=\"(item, index) in educationData\" :key=\"index\" class=\"education-item\">\r\n                <div class=\"education-label\">{{ item.name }}</div>\r\n                <div class=\"education-bar\">\r\n                  <div class=\"education-progress\" :style=\"`width: ${item.percentage}%`\"></div>\r\n                </div>\r\n                <div class=\"education-value\">{{ item.value }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 党派分布 -->\r\n        <div class=\"party-distribution-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">党派分布</span>\r\n          </div>\r\n          <div class=\"party-content\">\r\n            <div class=\"party-chart-container\">\r\n              <div class=\"party-pie-chart\">\r\n                <!-- 党派分布饼图占位 -->\r\n                <div class=\"party-chart-placeholder\">党派分布图</div>\r\n              </div>\r\n              <div class=\"party-legend\">\r\n                <div v-for=\"(item, index) in partyData\" :key=\"index\" class=\"party-legend-item\">\r\n                  <div class=\"legend-color\" :style=\"`background-color: ${item.color}`\"></div>\r\n                  <span class=\"legend-label\">{{ item.name }}</span>\r\n                  <span class=\"legend-percentage\">{{ item.percentage }}%</span>\r\n                  <span class=\"legend-value\">{{ item.value }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 讨论人员统计 -->\r\n        <div class=\"discussion-stats-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">讨论组人员统计</span>\r\n          </div>\r\n          <div class=\"discussion-content\">\r\n            <div class=\"discussion-chart-placeholder\">讨论人员统计图</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"right-panel\">\r\n        <!-- 界别分析 -->\r\n        <div class=\"sector-analysis-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">界别分布</span>\r\n          </div>\r\n          <div class=\"sector-content\">\r\n            <BarScrollChart id=\"sectorAnalysis\" :showCount=\"30\" :chart-data=\"sectorAnalysisData\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { useIndex } from '../screen.js'\r\nimport BarScrollChart from '../components/BarScrollChart.vue'\r\n\r\nexport default {\r\n  name: 'BigScreen',\r\n  components: {\r\n    BarScrollChart\r\n  },\r\n  data () {\r\n    return {\r\n      currentTime: '',\r\n      // 学历数据\r\n      educationData: [\r\n        { name: '博士', value: 101, percentage: 90 },\r\n        { name: '硕士', value: 89, percentage: 75 },\r\n        { name: '本科', value: 62, percentage: 60 },\r\n        { name: '大专', value: 45, percentage: 45 },\r\n        { name: '其他', value: 165, percentage: 100 },\r\n        { name: '研究生', value: 84, percentage: 70 }\r\n      ],\r\n      // 党派数据\r\n      partyData: [\r\n        { name: '中国共产党', value: 32, percentage: 15, color: '#FF6B6B' },\r\n        { name: '民革', value: 15, percentage: 8, color: '#4ECDC4' },\r\n        { name: '民盟', value: 14, percentage: 7, color: '#45B7D1' },\r\n        { name: '民建', value: 13, percentage: 6, color: '#96CEB4' },\r\n        { name: '民进', value: 12, percentage: 5, color: '#FFEAA7' },\r\n        { name: '农工党', value: 10, percentage: 4, color: '#DDA0DD' },\r\n        { name: '致公党', value: 8, percentage: 3, color: '#98D8C8' },\r\n        { name: '九三学社', value: 7, percentage: 3, color: '#F7DC6F' },\r\n        { name: '台盟', value: 6, percentage: 2, color: '#BB8FCE' },\r\n        { name: '无党派人士', value: 5, percentage: 2, color: '#85C1E9' }\r\n      ],\r\n      // 界别分析数据\r\n      sectorAnalysisData: [\r\n        { name: '经济界', value: 32 },\r\n        { name: '教育界', value: 15 },\r\n        { name: '科技界', value: 14 },\r\n        { name: '工商界', value: 13 },\r\n        { name: '医药卫生界', value: 12 },\r\n        { name: '社会科学界', value: 10 },\r\n        { name: '工会', value: 8 },\r\n        { name: '共青团', value: 7 },\r\n        { name: '妇联', value: 6 },\r\n        { name: '科协', value: 5 },\r\n        { name: '台联', value: 7 },\r\n        { name: '侨联', value: 3 },\r\n        { name: '文化艺术界', value: 24 },\r\n        { name: '体育界', value: 16 },\r\n        { name: '少数民族界', value: 20 },\r\n        { name: '宗教界', value: 27 },\r\n        { name: '特邀人士', value: 21 },\r\n        { name: '港澳台侨', value: 5 },\r\n        { name: '对外友好界', value: 19 },\r\n        { name: '社会福利和社会保障界', value: 12 },\r\n        { name: '社会治理和社会组织界', value: 21 }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n  },\r\n  mounted () {\r\n    this.initScreen()\r\n    this.updateTime()\r\n    this.timeInterval = setInterval(this.updateTime, 1000)\r\n  },\r\n  beforeDestroy () {\r\n    if (this.timeInterval) {\r\n      clearInterval(this.timeInterval)\r\n    }\r\n  },\r\n  methods: {\r\n    initScreen () {\r\n      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)\r\n      calcRate()\r\n      windowDraw()\r\n    },\r\n    updateTime () {\r\n      const now = new Date()\r\n      this.currentTime = now.toLocaleString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit',\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        second: '2-digit'\r\n      })\r\n    },\r\n    // 返回首页\r\n    goHome () {\r\n      this.$router.push({ path: '/homeBox' })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.big-screen {\r\n  width: 1920px;\r\n  height: 1080px;\r\n  position: relative;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  transform-origin: left top;\r\n  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;\r\n  background-size: cover;\r\n  background-position: center;\r\n\r\n  .screen-header {\r\n    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;\r\n    background-size: 100% 100%;\r\n    background-position: center;\r\n    height: 65px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 40px;\r\n\r\n    .header-left {\r\n      display: flex;\r\n      gap: 20px;\r\n      font-size: 14px;\r\n      color: #8cc8ff;\r\n      flex: 1;\r\n    }\r\n\r\n    .header-center {\r\n      width: 60%;\r\n      text-align: center;\r\n    }\r\n\r\n    .header-right {\r\n      flex: 1;\r\n      display: flex;\r\n      justify-content: flex-end;\r\n      align-items: center;\r\n\r\n      .header-buttons {\r\n        display: flex;\r\n        gap: 15px;\r\n\r\n        .header-btn {\r\n          padding: 8px 20px;\r\n          border-radius: 4px;\r\n          cursor: pointer;\r\n          font-size: 14px;\r\n          color: #FFFFFF;\r\n          background: linear-gradient(135deg, rgba(0, 181, 254, 0.8) 0%, rgba(0, 100, 200, 0.8) 100%);\r\n          border: 1px solid rgba(0, 181, 254, 0.6);\r\n          transition: all 0.3s ease;\r\n          position: relative;\r\n          overflow: hidden;\r\n\r\n          &::before {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0;\r\n            left: -100%;\r\n            width: 100%;\r\n            height: 100%;\r\n            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n            transition: left 0.5s;\r\n          }\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 1) 0%, rgba(0, 120, 220, 1) 100%);\r\n            border-color: rgba(0, 181, 254, 1);\r\n            transform: translateY(-2px);\r\n            box-shadow: 0 4px 12px rgba(0, 181, 254, 0.4);\r\n\r\n            &::before {\r\n              left: 100%;\r\n            }\r\n          }\r\n\r\n          &.current-module-btn {\r\n            background: linear-gradient(135deg, rgba(255, 214, 0, 0.9) 0%, rgba(255, 165, 0, 0.9) 100%);\r\n            border-color: rgba(255, 214, 0, 0.8);\r\n            color: #333;\r\n\r\n            &:hover {\r\n              background: linear-gradient(135deg, rgba(255, 214, 0, 1) 0%, rgba(255, 165, 0, 1) 100%);\r\n              border-color: rgba(255, 214, 0, 1);\r\n            }\r\n          }\r\n\r\n          span {\r\n            position: relative;\r\n            z-index: 1;\r\n            font-weight: 500;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .screen-content {\r\n    height: calc(100% - 65px);\r\n    display: flex;\r\n    padding: 20px;\r\n    gap: 20px;\r\n\r\n    .header_box {\r\n      position: absolute;\r\n      top: 15px;\r\n      left: 24px;\r\n      right: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n\r\n      .header_text_left {\r\n        font-weight: bold;\r\n        font-size: 20px;\r\n        color: #FFFFFF;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .header_text_right {\r\n        font-size: 15px;\r\n        color: #FFD600;\r\n      }\r\n\r\n      .header_text_center {\r\n        font-size: 15px;\r\n        color: #FFFFFF;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          font-weight: 500;\r\n          font-size: 24px;\r\n          color: #02FBFB;\r\n          margin: 0 10px 0 6px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .left-panel {\r\n      flex: 1;\r\n      display: grid;\r\n      grid-template-columns: 1fr 1fr 1fr;\r\n      grid-template-rows: 1fr 1fr 1fr;\r\n      gap: 20px;\r\n      height: 100%;\r\n    }\r\n\r\n    .right-panel {\r\n      width: 465px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 20px;\r\n    }\r\n\r\n    // 左侧面板样式\r\n    .left-panel {\r\n\r\n      // 委员数量\r\n      .committee-count-section {\r\n        background: url('../../../assets/largeScreen/gender_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 1; // 第一列\r\n        grid-row: 1; // 第一行\r\n\r\n        .count-content {\r\n          display: flex;\r\n          justify-content: space-around;\r\n          align-items: center;\r\n          height: 100%;\r\n          margin-top: 30px;\r\n\r\n          .count-item {\r\n            text-align: center;\r\n\r\n            .count-value {\r\n              font-size: 48px;\r\n              font-weight: bold;\r\n              color: #00D4FF;\r\n              margin-bottom: 10px;\r\n              text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);\r\n            }\r\n\r\n            .count-label {\r\n              font-size: 16px;\r\n              color: #FFFFFF;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 性别比例\r\n      .gender-ratio-section {\r\n        background: url('../../../assets/largeScreen/gender_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 2; // 第二列\r\n        grid-row: 1; // 第一行\r\n\r\n        .gender-content {\r\n          margin-top: 30px;\r\n          height: 100%;\r\n\r\n          .gender-chart {\r\n            display: flex;\r\n            justify-content: space-around;\r\n            align-items: center;\r\n            height: 100%;\r\n\r\n            .ratio-item {\r\n              text-align: center;\r\n\r\n              .ratio-circle {\r\n                width: 80px;\r\n                height: 80px;\r\n                border-radius: 50%;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                font-size: 18px;\r\n                font-weight: bold;\r\n                color: #FFFFFF;\r\n                margin-bottom: 10px;\r\n\r\n                &.male {\r\n                  background: conic-gradient(#00D4FF 0% 70%, rgba(0, 212, 255, 0.2) 70% 100%);\r\n                }\r\n\r\n                &.female {\r\n                  background: conic-gradient(#FFD600 0% 30%, rgba(255, 214, 0, 0.2) 30% 100%);\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 年龄\r\n      .age-section {\r\n        background: url('../../../assets/largeScreen/age_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 3; // 第三列\r\n        grid-row: 1; // 第一行\r\n\r\n        .age-content {\r\n          margin-top: 30px;\r\n          height: calc(100% - 50px);\r\n\r\n          .age-pie-chart {\r\n            height: 100%;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n\r\n            .age-chart-placeholder {\r\n              color: #FFFFFF;\r\n              font-size: 16px;\r\n              text-align: center;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 学历\r\n      .education-section {\r\n        background: url('../../../assets/largeScreen/education_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 1; // 第一列\r\n        grid-row: 2; // 第二行\r\n\r\n        .education-content {\r\n          margin-top: 30px;\r\n          height: calc(100% - 50px);\r\n\r\n          .education-bars {\r\n            height: 100%;\r\n            overflow-y: auto;\r\n\r\n            .education-item {\r\n              display: flex;\r\n              align-items: center;\r\n              margin-bottom: 15px;\r\n              gap: 10px;\r\n\r\n              .education-label {\r\n                width: 60px;\r\n                font-size: 14px;\r\n                color: #FFFFFF;\r\n                flex-shrink: 0;\r\n              }\r\n\r\n              .education-bar {\r\n                flex: 1;\r\n                height: 8px;\r\n                background: rgba(0, 181, 254, 0.2);\r\n                border-radius: 4px;\r\n                overflow: hidden;\r\n\r\n                .education-progress {\r\n                  height: 100%;\r\n                  background: linear-gradient(90deg, #00D4FF 0%, #0080FF 100%);\r\n                  border-radius: 4px;\r\n                  transition: width 0.3s ease;\r\n                }\r\n              }\r\n\r\n              .education-value {\r\n                width: 30px;\r\n                font-size: 14px;\r\n                color: #00D4FF;\r\n                text-align: right;\r\n                flex-shrink: 0;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 党派分布\r\n      .party-distribution-section {\r\n        background: url('../../../assets/largeScreen/party_distribution_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 2 / 4; // 跨越第2和第3列（在第二行）\r\n        grid-row: 2; // 明确指定在第二行\r\n\r\n        .party-content {\r\n          margin-top: 30px;\r\n          height: calc(100% - 50px);\r\n\r\n          .party-chart-container {\r\n            display: flex;\r\n            height: 100%;\r\n            gap: 20px;\r\n\r\n            .party-pie-chart {\r\n              width: 200px;\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: center;\r\n\r\n              .party-chart-placeholder {\r\n                color: #FFFFFF;\r\n                font-size: 16px;\r\n                text-align: center;\r\n              }\r\n            }\r\n\r\n            .party-legend {\r\n              flex: 1;\r\n              display: grid;\r\n              grid-template-columns: 1fr 1fr;\r\n              gap: 8px;\r\n              max-height: 100%;\r\n              overflow-y: auto;\r\n\r\n              .party-legend-item {\r\n                display: flex;\r\n                align-items: center;\r\n                gap: 8px;\r\n                font-size: 12px;\r\n\r\n                .legend-color {\r\n                  width: 12px;\r\n                  height: 12px;\r\n                  border-radius: 2px;\r\n                  flex-shrink: 0;\r\n                }\r\n\r\n                .legend-label {\r\n                  color: #FFFFFF;\r\n                  flex: 1;\r\n                  min-width: 0;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  white-space: nowrap;\r\n                }\r\n\r\n                .legend-percentage {\r\n                  color: #00D4FF;\r\n                  font-weight: 500;\r\n                  width: 30px;\r\n                  text-align: right;\r\n                }\r\n\r\n                .legend-value {\r\n                  color: #FFD600;\r\n                  font-weight: 500;\r\n                  width: 25px;\r\n                  text-align: right;\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 讨论人员统计\r\n      .discussion-stats-section {\r\n        background: url('../../../assets/largeScreen/discussion_stats_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 1 / -1; // 跨越三列（讨论组人员统计在第三行）\r\n        grid-row: 3; // 明确指定在第三行\r\n\r\n        .discussion-content {\r\n          margin-top: 30px;\r\n          height: calc(100% - 50px);\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n\r\n          .discussion-chart-placeholder {\r\n            color: #FFFFFF;\r\n            font-size: 16px;\r\n            text-align: center;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .right-panel {\r\n\r\n      // 界别分析\r\n      .sector-analysis-section {\r\n        background: url('../../../assets/largeScreen/sector_analysis_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        height: 100%;\r\n\r\n        .sector-content {\r\n          margin-top: 30px;\r\n          height: calc(100% - 25px);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}