{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\committeeStatistics\\committeeStatisticsBox.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\committeeStatistics\\committeeStatisticsBox.vue", "mtime": 1755587391590}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAqJA;AAEA;EACAA,iBADA;EAEAC,cAFA;;EAIAC;IACA;MACAC,eADA;MAEA;MACAC,gBACA;QAAAJ;QAAAK;QAAAC;MAAA,CADA,EAEA;QAAAN;QAAAK;QAAAC;MAAA,CAFA,EAGA;QAAAN;QAAAK;QAAAC;MAAA,CAHA,EAIA;QAAAN;QAAAK;QAAAC;MAAA,CAJA,EAKA;QAAAN;QAAAK;QAAAC;MAAA,CALA,EAMA;QAAAN;QAAAK;QAAAC;MAAA,CANA,CAHA;MAWA;MACAC,YACA;QAAAP;QAAAK;QAAAC;QAAAE;MAAA,CADA,EAEA;QAAAR;QAAAK;QAAAC;QAAAE;MAAA,CAFA,EAGA;QAAAR;QAAAK;QAAAC;QAAAE;MAAA,CAHA,EAIA;QAAAR;QAAAK;QAAAC;QAAAE;MAAA,CAJA,EAKA;QAAAR;QAAAK;QAAAC;QAAAE;MAAA,CALA,EAMA;QAAAR;QAAAK;QAAAC;QAAAE;MAAA,CANA,EAOA;QAAAR;QAAAK;QAAAC;QAAAE;MAAA,CAPA,EAQA;QAAAR;QAAAK;QAAAC;QAAAE;MAAA,CARA,EASA;QAAAR;QAAAK;QAAAC;QAAAE;MAAA,CATA,EAUA;QAAAR;QAAAK;QAAAC;QAAAE;MAAA,CAVA,CAZA;MAwBA;MACAC,aACA;QAAAT;QAAAK;QAAAC;MAAA,CADA,EAEA;QAAAN;QAAAK;QAAAC;MAAA,CAFA,EAGA;QAAAN;QAAAK;QAAAC;MAAA,CAHA,EAIA;QAAAN;QAAAK;QAAAC;MAAA,CAJA,EAKA;QAAAN;QAAAK;QAAAC;MAAA,CALA,EAMA;QAAAN;QAAAK;QAAAC;MAAA,CANA,EAOA;QAAAN;QAAAK;QAAAC;MAAA,CAPA,EAQA;QAAAN;QAAAK;QAAAC;MAAA,CARA,EASA;QAAAN;QAAAK;QAAAC;MAAA,CATA,EAUA;QAAAN;QAAAK;QAAAC;MAAA,CAVA,EAWA;QAAAN;QAAAK;QAAAC;MAAA,CAXA,EAYA;QAAAN;QAAAK;QAAAC;MAAA,CAZA,EAaA;QAAAN;QAAAK;QAAAC;MAAA,CAbA,EAcA;QAAAN;QAAAK;QAAAC;MAAA,CAdA,EAeA;QAAAN;QAAAK;QAAAC;MAAA,CAfA,EAgBA;QAAAN;QAAAK;QAAAC;MAAA,CAhBA,EAiBA;QAAAN;QAAAK;QAAAC;MAAA,CAjBA,EAkBA;QAAAN;QAAAK;QAAAC;MAAA,CAlBA,EAmBA;QAAAN;QAAAK;QAAAC;MAAA,CAnBA,EAoBA;QAAAN;QAAAK;QAAAC;MAAA,CApBA,EAqBA;QAAAN;QAAAK;QAAAC;MAAA,CArBA;IAzBA;EAiDA,CAtDA;;EAuDAI,YAvDA;;EAyDAC;IACA;IACA;IACA;EACA,CA7DA;;EA8DAC;IACA;MACAC;IACA;EACA,CAlEA;;EAmEAC;IACAC;MACA;QAAAC;QAAAC;MAAA;MACAD;MACAC;IACA,CALA;;IAMAC;MACA;MACA;QACAC,eADA;QAEAC,gBAFA;QAGAC,cAHA;QAIAC,eAJA;QAKAC,iBALA;QAMAC;MANA;IAQA,CAhBA;;IAiBA;IACAC;MACA;QAAAC;MAAA;IACA;;EApBA;AAnEA", "names": ["name", "components", "data", "currentTime", "educationData", "value", "percentage", "partyData", "color", "sectorData", "computed", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "initScreen", "calcRate", "windowDraw", "updateTime", "year", "month", "day", "hour", "minute", "second", "goHome", "path"], "sourceRoot": "src/views/smartBrainLargeScreen/committeeStatistics", "sources": ["committeeStatisticsBox.vue"], "sourcesContent": ["<template>\r\n  <div class=\"big-screen\" ref=\"bigScreen\">\r\n    <div class=\"screen-header\">\r\n      <div class=\"header-left\">\r\n        <span class=\"date-time\">{{ currentTime }}</span>\r\n        <span class=\"weather\">晴 24℃ 东南风</span>\r\n      </div>\r\n      <div class=\"header-center\">\r\n        <img src=\"../../../assets/largeScreen/top_header_txt.png\" alt=\"\" style=\"height: 50px;\">\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <div class=\"header-buttons\">\r\n          <div class=\"header-btn area-select-btn\">\r\n            <span>选择地区</span>\r\n          </div>\r\n          <div class=\"header-btn current-module-btn\">\r\n            <span>委员统计</span>\r\n          </div>\r\n          <div class=\"header-btn home-btn\" @click=\"goHome\">\r\n            <span>返回首页</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"screen-content\">\r\n      <div class=\"left-panel\">\r\n        <!-- 委员数量 -->\r\n        <div class=\"committee-count-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">委员数量</span>\r\n          </div>\r\n          <div class=\"count-content\">\r\n            <div class=\"count-item\">\r\n              <div class=\"count-value\">418</div>\r\n              <div class=\"count-label\">委员总数</div>\r\n            </div>\r\n            <div class=\"count-item\">\r\n              <div class=\"count-value\">42</div>\r\n              <div class=\"count-label\">政协常委</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 性别比例 -->\r\n        <div class=\"gender-ratio-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">性别比例</span>\r\n          </div>\r\n          <div class=\"gender-content\">\r\n            <div class=\"gender-chart\">\r\n              <div class=\"ratio-item\">\r\n                <div class=\"ratio-circle male\">70%</div>\r\n              </div>\r\n              <div class=\"ratio-item\">\r\n                <div class=\"ratio-circle female\">30%</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 年龄 -->\r\n        <div class=\"age-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">年龄</span>\r\n          </div>\r\n          <div class=\"age-content\">\r\n            <div class=\"age-pie-chart\">\r\n              <!-- 年龄饼图占位 -->\r\n              <div class=\"age-chart-placeholder\">年龄分布图</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 学历 -->\r\n        <div class=\"education-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">学历</span>\r\n          </div>\r\n          <div class=\"education-content\">\r\n            <div class=\"education-bars\">\r\n              <div v-for=\"(item, index) in educationData\" :key=\"index\" class=\"education-item\">\r\n                <div class=\"education-label\">{{ item.name }}</div>\r\n                <div class=\"education-bar\">\r\n                  <div class=\"education-progress\" :style=\"`width: ${item.percentage}%`\"></div>\r\n                </div>\r\n                <div class=\"education-value\">{{ item.value }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 党派分布 -->\r\n        <div class=\"party-distribution-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">党派分布</span>\r\n          </div>\r\n          <div class=\"party-content\">\r\n            <div class=\"party-chart-container\">\r\n              <div class=\"party-pie-chart\">\r\n                <!-- 党派分布饼图占位 -->\r\n                <div class=\"party-chart-placeholder\">党派分布图</div>\r\n              </div>\r\n              <div class=\"party-legend\">\r\n                <div v-for=\"(item, index) in partyData\" :key=\"index\" class=\"party-legend-item\">\r\n                  <div class=\"legend-color\" :style=\"`background-color: ${item.color}`\"></div>\r\n                  <span class=\"legend-label\">{{ item.name }}</span>\r\n                  <span class=\"legend-percentage\">{{ item.percentage }}%</span>\r\n                  <span class=\"legend-value\">{{ item.value }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 讨论人员统计 -->\r\n        <div class=\"discussion-stats-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">讨论人员统计</span>\r\n          </div>\r\n          <div class=\"discussion-content\">\r\n            <div class=\"discussion-chart-placeholder\">讨论人员统计图</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"right-panel\">\r\n        <!-- 界别分析 -->\r\n        <div class=\"sector-analysis-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">界别分析</span>\r\n          </div>\r\n          <div class=\"sector-content\">\r\n            <div class=\"sector-bars\">\r\n              <div v-for=\"(item, index) in sectorData\" :key=\"index\" class=\"sector-item\">\r\n                <div class=\"sector-label\">{{ item.name }}</div>\r\n                <div class=\"sector-bar\">\r\n                  <div class=\"sector-progress\" :style=\"`width: ${item.percentage}%`\"></div>\r\n                </div>\r\n                <div class=\"sector-value\">{{ item.value }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { useIndex } from '../screen.js'\r\n\r\nexport default {\r\n  name: 'BigScreen',\r\n  components: {\r\n  },\r\n  data () {\r\n    return {\r\n      currentTime: '',\r\n      // 学历数据\r\n      educationData: [\r\n        { name: '博士', value: 101, percentage: 90 },\r\n        { name: '硕士', value: 89, percentage: 75 },\r\n        { name: '本科', value: 62, percentage: 60 },\r\n        { name: '大专', value: 45, percentage: 45 },\r\n        { name: '其他', value: 165, percentage: 100 },\r\n        { name: '研究生', value: 84, percentage: 70 }\r\n      ],\r\n      // 党派数据\r\n      partyData: [\r\n        { name: '中国共产党', value: 32, percentage: 15, color: '#FF6B6B' },\r\n        { name: '民革', value: 15, percentage: 8, color: '#4ECDC4' },\r\n        { name: '民盟', value: 14, percentage: 7, color: '#45B7D1' },\r\n        { name: '民建', value: 13, percentage: 6, color: '#96CEB4' },\r\n        { name: '民进', value: 12, percentage: 5, color: '#FFEAA7' },\r\n        { name: '农工党', value: 10, percentage: 4, color: '#DDA0DD' },\r\n        { name: '致公党', value: 8, percentage: 3, color: '#98D8C8' },\r\n        { name: '九三学社', value: 7, percentage: 3, color: '#F7DC6F' },\r\n        { name: '台盟', value: 6, percentage: 2, color: '#BB8FCE' },\r\n        { name: '无党派人士', value: 5, percentage: 2, color: '#85C1E9' }\r\n      ],\r\n      // 界别分析数据\r\n      sectorData: [\r\n        { name: '经济界', value: 32, percentage: 90 },\r\n        { name: '教育界', value: 15, percentage: 45 },\r\n        { name: '科技界', value: 14, percentage: 40 },\r\n        { name: '工商界', value: 13, percentage: 38 },\r\n        { name: '医药卫生界', value: 12, percentage: 35 },\r\n        { name: '社会科学界', value: 10, percentage: 30 },\r\n        { name: '工会', value: 8, percentage: 25 },\r\n        { name: '共青团', value: 7, percentage: 20 },\r\n        { name: '妇联', value: 6, percentage: 18 },\r\n        { name: '科协', value: 5, percentage: 15 },\r\n        { name: '台联', value: 7, percentage: 20 },\r\n        { name: '侨联', value: 3, percentage: 10 },\r\n        { name: '文化艺术界', value: 24, percentage: 70 },\r\n        { name: '体育界', value: 16, percentage: 48 },\r\n        { name: '少数民族界', value: 20, percentage: 60 },\r\n        { name: '宗教界', value: 27, percentage: 75 },\r\n        { name: '特邀人士', value: 21, percentage: 65 },\r\n        { name: '港澳台侨', value: 5, percentage: 15 },\r\n        { name: '对外友好界', value: 19, percentage: 55 },\r\n        { name: '社会福利和社会保障界', value: 12, percentage: 35 },\r\n        { name: '社会治理和社会组织界', value: 21, percentage: 65 }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n  },\r\n  mounted () {\r\n    this.initScreen()\r\n    this.updateTime()\r\n    this.timeInterval = setInterval(this.updateTime, 1000)\r\n  },\r\n  beforeDestroy () {\r\n    if (this.timeInterval) {\r\n      clearInterval(this.timeInterval)\r\n    }\r\n  },\r\n  methods: {\r\n    initScreen () {\r\n      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)\r\n      calcRate()\r\n      windowDraw()\r\n    },\r\n    updateTime () {\r\n      const now = new Date()\r\n      this.currentTime = now.toLocaleString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit',\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        second: '2-digit'\r\n      })\r\n    },\r\n    // 返回首页\r\n    goHome () {\r\n      this.$router.push({ path: '/homeBox' })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.big-screen {\r\n  width: 1920px;\r\n  height: 1080px;\r\n  position: relative;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  transform-origin: left top;\r\n  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;\r\n  background-size: cover;\r\n  background-position: center;\r\n\r\n  .screen-header {\r\n    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;\r\n    background-size: 100% 100%;\r\n    background-position: center;\r\n    height: 65px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 40px;\r\n\r\n    .header-left {\r\n      display: flex;\r\n      gap: 20px;\r\n      font-size: 14px;\r\n      color: #8cc8ff;\r\n      flex: 1;\r\n    }\r\n\r\n    .header-center {\r\n      width: 60%;\r\n      text-align: center;\r\n    }\r\n\r\n    .header-right {\r\n      flex: 1;\r\n      display: flex;\r\n      justify-content: flex-end;\r\n      align-items: center;\r\n\r\n      .header-buttons {\r\n        display: flex;\r\n        gap: 15px;\r\n\r\n        .header-btn {\r\n          padding: 8px 20px;\r\n          border-radius: 4px;\r\n          cursor: pointer;\r\n          font-size: 14px;\r\n          color: #FFFFFF;\r\n          background: linear-gradient(135deg, rgba(0, 181, 254, 0.8) 0%, rgba(0, 100, 200, 0.8) 100%);\r\n          border: 1px solid rgba(0, 181, 254, 0.6);\r\n          transition: all 0.3s ease;\r\n          position: relative;\r\n          overflow: hidden;\r\n\r\n          &::before {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0;\r\n            left: -100%;\r\n            width: 100%;\r\n            height: 100%;\r\n            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n            transition: left 0.5s;\r\n          }\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 1) 0%, rgba(0, 120, 220, 1) 100%);\r\n            border-color: rgba(0, 181, 254, 1);\r\n            transform: translateY(-2px);\r\n            box-shadow: 0 4px 12px rgba(0, 181, 254, 0.4);\r\n\r\n            &::before {\r\n              left: 100%;\r\n            }\r\n          }\r\n\r\n          &.current-module-btn {\r\n            background: linear-gradient(135deg, rgba(255, 214, 0, 0.9) 0%, rgba(255, 165, 0, 0.9) 100%);\r\n            border-color: rgba(255, 214, 0, 0.8);\r\n            color: #333;\r\n\r\n            &:hover {\r\n              background: linear-gradient(135deg, rgba(255, 214, 0, 1) 0%, rgba(255, 165, 0, 1) 100%);\r\n              border-color: rgba(255, 214, 0, 1);\r\n            }\r\n          }\r\n\r\n          span {\r\n            position: relative;\r\n            z-index: 1;\r\n            font-weight: 500;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .screen-content {\r\n    height: calc(100% - 65px);\r\n    display: flex;\r\n    padding: 20px;\r\n    gap: 20px;\r\n\r\n    .header_box {\r\n      position: absolute;\r\n      top: 15px;\r\n      left: 24px;\r\n      right: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n\r\n      .header_text_left {\r\n        font-weight: bold;\r\n        font-size: 20px;\r\n        color: #FFFFFF;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .header_text_right {\r\n        font-size: 15px;\r\n        color: #FFD600;\r\n      }\r\n\r\n      .header_text_center {\r\n        font-size: 15px;\r\n        color: #FFFFFF;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          font-weight: 500;\r\n          font-size: 24px;\r\n          color: #02FBFB;\r\n          margin: 0 10px 0 6px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .left-panel {\r\n      width: 60%;\r\n      display: grid;\r\n      grid-template-columns: 1fr 1fr;\r\n      grid-template-rows: auto auto auto;\r\n      gap: 20px;\r\n      height: 100%;\r\n    }\r\n\r\n    .right-panel {\r\n      width: 40%;\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 20px;\r\n    }\r\n\r\n    // 左侧面板样式\r\n    .left-panel {\r\n\r\n      // 委员数量\r\n      .committee-count-section {\r\n        background: linear-gradient(135deg, rgba(0, 50, 100, 0.8) 0%, rgba(0, 30, 60, 0.9) 100%);\r\n        border: 1px solid rgba(0, 181, 254, 0.3);\r\n        border-radius: 8px;\r\n        position: relative;\r\n        padding: 20px;\r\n        height: 200px;\r\n\r\n        .count-content {\r\n          display: flex;\r\n          justify-content: space-around;\r\n          align-items: center;\r\n          height: 100%;\r\n          margin-top: 30px;\r\n\r\n          .count-item {\r\n            text-align: center;\r\n\r\n            .count-value {\r\n              font-size: 48px;\r\n              font-weight: bold;\r\n              color: #00D4FF;\r\n              margin-bottom: 10px;\r\n              text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);\r\n            }\r\n\r\n            .count-label {\r\n              font-size: 16px;\r\n              color: #FFFFFF;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 性别比例\r\n      .gender-ratio-section {\r\n        background: linear-gradient(135deg, rgba(0, 50, 100, 0.8) 0%, rgba(0, 30, 60, 0.9) 100%);\r\n        border: 1px solid rgba(0, 181, 254, 0.3);\r\n        border-radius: 8px;\r\n        position: relative;\r\n        padding: 20px;\r\n        height: 200px;\r\n\r\n        .gender-content {\r\n          margin-top: 30px;\r\n          height: 100%;\r\n\r\n          .gender-chart {\r\n            display: flex;\r\n            justify-content: space-around;\r\n            align-items: center;\r\n            height: 100%;\r\n\r\n            .ratio-item {\r\n              text-align: center;\r\n\r\n              .ratio-circle {\r\n                width: 80px;\r\n                height: 80px;\r\n                border-radius: 50%;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                font-size: 18px;\r\n                font-weight: bold;\r\n                color: #FFFFFF;\r\n                margin-bottom: 10px;\r\n\r\n                &.male {\r\n                  background: conic-gradient(#00D4FF 0% 70%, rgba(0, 212, 255, 0.2) 70% 100%);\r\n                }\r\n\r\n                &.female {\r\n                  background: conic-gradient(#FFD600 0% 30%, rgba(255, 214, 0, 0.2) 30% 100%);\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 年龄\r\n      .age-section {\r\n        background: linear-gradient(135deg, rgba(0, 50, 100, 0.8) 0%, rgba(0, 30, 60, 0.9) 100%);\r\n        border: 1px solid rgba(0, 181, 254, 0.3);\r\n        border-radius: 8px;\r\n        position: relative;\r\n        padding: 20px;\r\n        height: 250px;\r\n\r\n        .age-content {\r\n          margin-top: 30px;\r\n          height: calc(100% - 50px);\r\n\r\n          .age-pie-chart {\r\n            height: 100%;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n\r\n            .age-chart-placeholder {\r\n              color: #FFFFFF;\r\n              font-size: 16px;\r\n              text-align: center;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 学历\r\n      .education-section {\r\n        background: linear-gradient(135deg, rgba(0, 50, 100, 0.8) 0%, rgba(0, 30, 60, 0.9) 100%);\r\n        border: 1px solid rgba(0, 181, 254, 0.3);\r\n        border-radius: 8px;\r\n        position: relative;\r\n        padding: 20px;\r\n        height: 250px;\r\n\r\n        .education-content {\r\n          margin-top: 30px;\r\n          height: calc(100% - 50px);\r\n\r\n          .education-bars {\r\n            height: 100%;\r\n            overflow-y: auto;\r\n\r\n            .education-item {\r\n              display: flex;\r\n              align-items: center;\r\n              margin-bottom: 15px;\r\n              gap: 10px;\r\n\r\n              .education-label {\r\n                width: 60px;\r\n                font-size: 14px;\r\n                color: #FFFFFF;\r\n                flex-shrink: 0;\r\n              }\r\n\r\n              .education-bar {\r\n                flex: 1;\r\n                height: 8px;\r\n                background: rgba(0, 181, 254, 0.2);\r\n                border-radius: 4px;\r\n                overflow: hidden;\r\n\r\n                .education-progress {\r\n                  height: 100%;\r\n                  background: linear-gradient(90deg, #00D4FF 0%, #0080FF 100%);\r\n                  border-radius: 4px;\r\n                  transition: width 0.3s ease;\r\n                }\r\n              }\r\n\r\n              .education-value {\r\n                width: 30px;\r\n                font-size: 14px;\r\n                color: #00D4FF;\r\n                text-align: right;\r\n                flex-shrink: 0;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 党派分布\r\n      .party-distribution-section {\r\n        background: linear-gradient(135deg, rgba(0, 50, 100, 0.8) 0%, rgba(0, 30, 60, 0.9) 100%);\r\n        border: 1px solid rgba(0, 181, 254, 0.3);\r\n        border-radius: 8px;\r\n        position: relative;\r\n        padding: 20px;\r\n        height: 300px;\r\n        grid-column: 1 / -1; // 跨越两列\r\n\r\n        .party-content {\r\n          margin-top: 30px;\r\n          height: calc(100% - 50px);\r\n\r\n          .party-chart-container {\r\n            display: flex;\r\n            height: 100%;\r\n            gap: 20px;\r\n\r\n            .party-pie-chart {\r\n              width: 200px;\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: center;\r\n\r\n              .party-chart-placeholder {\r\n                color: #FFFFFF;\r\n                font-size: 16px;\r\n                text-align: center;\r\n              }\r\n            }\r\n\r\n            .party-legend {\r\n              flex: 1;\r\n              display: grid;\r\n              grid-template-columns: 1fr 1fr;\r\n              gap: 8px;\r\n              max-height: 100%;\r\n              overflow-y: auto;\r\n\r\n              .party-legend-item {\r\n                display: flex;\r\n                align-items: center;\r\n                gap: 8px;\r\n                font-size: 12px;\r\n\r\n                .legend-color {\r\n                  width: 12px;\r\n                  height: 12px;\r\n                  border-radius: 2px;\r\n                  flex-shrink: 0;\r\n                }\r\n\r\n                .legend-label {\r\n                  color: #FFFFFF;\r\n                  flex: 1;\r\n                  min-width: 0;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  white-space: nowrap;\r\n                }\r\n\r\n                .legend-percentage {\r\n                  color: #00D4FF;\r\n                  font-weight: 500;\r\n                  width: 30px;\r\n                  text-align: right;\r\n                }\r\n\r\n                .legend-value {\r\n                  color: #FFD600;\r\n                  font-weight: 500;\r\n                  width: 25px;\r\n                  text-align: right;\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 讨论人员统计\r\n      .discussion-stats-section {\r\n        background: linear-gradient(135deg, rgba(0, 50, 100, 0.8) 0%, rgba(0, 30, 60, 0.9) 100%);\r\n        border: 1px solid rgba(0, 181, 254, 0.3);\r\n        border-radius: 8px;\r\n        position: relative;\r\n        padding: 20px;\r\n        height: 300px;\r\n        grid-column: 1 / -1; // 跨越两列\r\n\r\n        .discussion-content {\r\n          margin-top: 30px;\r\n          height: calc(100% - 50px);\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n\r\n          .discussion-chart-placeholder {\r\n            color: #FFFFFF;\r\n            font-size: 16px;\r\n            text-align: center;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .right-panel {\r\n\r\n      // 界别分析\r\n      .sector-analysis-section {\r\n        background: linear-gradient(135deg, rgba(0, 50, 100, 0.8) 0%, rgba(0, 30, 60, 0.9) 100%);\r\n        border: 1px solid rgba(0, 181, 254, 0.3);\r\n        border-radius: 8px;\r\n        position: relative;\r\n        padding: 20px;\r\n        height: 100%;\r\n\r\n        .sector-content {\r\n          margin-top: 30px;\r\n          height: calc(100% - 50px);\r\n\r\n          .sector-bars {\r\n            height: 100%;\r\n            overflow-y: auto;\r\n            padding-right: 10px;\r\n\r\n            &::-webkit-scrollbar {\r\n              width: 6px;\r\n            }\r\n\r\n            &::-webkit-scrollbar-track {\r\n              background: rgba(0, 30, 60, 0.3);\r\n              border-radius: 3px;\r\n            }\r\n\r\n            &::-webkit-scrollbar-thumb {\r\n              background: rgba(0, 212, 255, 0.4);\r\n              border-radius: 3px;\r\n\r\n              &:hover {\r\n                background: rgba(0, 212, 255, 0.6);\r\n              }\r\n            }\r\n\r\n            .sector-item {\r\n              display: flex;\r\n              align-items: center;\r\n              margin-bottom: 12px;\r\n              gap: 10px;\r\n              font-size: 12px;\r\n\r\n              .sector-label {\r\n                width: 120px;\r\n                color: #FFFFFF;\r\n                flex-shrink: 0;\r\n                overflow: hidden;\r\n                text-overflow: ellipsis;\r\n                white-space: nowrap;\r\n              }\r\n\r\n              .sector-bar {\r\n                flex: 1;\r\n                height: 6px;\r\n                background: rgba(0, 181, 254, 0.2);\r\n                border-radius: 3px;\r\n                overflow: hidden;\r\n\r\n                .sector-progress {\r\n                  height: 100%;\r\n                  background: linear-gradient(90deg, #00D4FF 0%, #0080FF 100%);\r\n                  border-radius: 3px;\r\n                  transition: width 0.3s ease;\r\n                }\r\n              }\r\n\r\n              .sector-value {\r\n                width: 25px;\r\n                color: #00D4FF;\r\n                text-align: right;\r\n                flex-shrink: 0;\r\n                font-weight: 500;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}