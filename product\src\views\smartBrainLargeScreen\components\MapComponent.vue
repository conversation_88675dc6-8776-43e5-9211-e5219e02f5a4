<template>
  <div class="map-container">
    <div class="map" id="homeMap"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
require('echarts/theme/macarons') // 引入主题

export default {
  name: 'MapComponent',
  props: ['data', 'areaId', 'areaName'],
  data () {
    return {
      options: {
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            const value = params.value !== undefined && params.value !== null ? params.value : 0
            return `<div style="padding: 8px;">
              <div style="color: #00D4FF; margin-bottom: 4px;">${params.name || ''}</div>
              <div style="color: #FFFFFF;">${value}</div>
            </div>`
          },
          backgroundColor: 'rgba(0, 20, 40, 0.95)',
          borderColor: '#00D4FF',
          borderWidth: 2,
          borderRadius: 8,
          shadowColor: 'rgba(0, 212, 255, 0.3)',
          shadowBlur: 10,
          textStyle: {
            color: '#FFFFFF',
            fontSize: 11,
            fontFamily: 'Microsoft YaHei, Arial, sans-serif'
          }
        },
        geo: {
          map: '智慧人大',
          layoutCenter: ['50%', '50%'],
          layoutSize: '90%',
          roam: false,
          itemStyle: {
            borderWidth: 2,
            borderColor: 'rgba(0, 181, 254, 1)',
            shadowColor: 'rgba(0, 181, 254, 1)',
            shadowBlur: 0,
            shadowOffsetX: 0,
            shadowOffsetY: 10
          },
          emphasis: {
            borderWidth: 2,
            borderColor: 'rgba(0, 181, 254, 1)',
            shadowColor: 'rgba(0, 181, 254, .1)',
            shadowBlur: 0,
            shadowOffsetX: 0,
            shadowOffsetY: 10,
            areaColor: {
              type: 'radial',
              x: 0.5,
              y: 0.1,
              r: 0.9,
              colorStops: [
                { offset: 0, color: 'rgba(0, 181, 254, 0.6)' },
                { offset: 1, color: 'rgba(0, 181, 254, 0.6)' }
              ]
            }
          }
        },
        series: []
      },
      echartObjRef: null,
      regionList: []
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.initMap()
    })
  },
  methods: {
    splitFileName (text) {
      var pattern = /\.{1}[a-z]{1,}$/
      return text.slice(text.lastIndexOf('/') + 1, pattern.exec(text).index)
    },

    async initMap (id = this.areaId) {
      const mapJson = await import('./qingdao.json')
      let geoJson = mapJson.default
      if (id && id !== '370200' && this.data) {
        const area = this.data.find(a => a.areaId === id)
        if (area) {
          const feature = geoJson.features.find(f => f.properties && (f.properties.adcode === area.adcode))
          if (feature) {
            geoJson = { ...geoJson, features: [feature] }
          }
        }
      }
      this.renderMap(geoJson, id)
    },

    renderMap (JSONData, areaId = this.areaId) {
      const dom = document.getElementById('homeMap')
      if (!dom) return
      dom.removeAttribute('_echarts_instance_')
      const echartObj = echarts.init(dom, 'macarons')
      this.echartObjRef = echartObj
      echarts.registerMap('智慧人大', JSONData)
      echartObj.setOption({
        ...this.getOptions(areaId),
        series: [this.getSeriesData(), this.getMapSeries(areaId)]
      })
      echartObj.off('click')
      echartObj.on('click', (param) => {
        // areaName.value = param.name
        const area = this.data?.find(a => a.name === param.name)
        // emit('mapClick', { name: param.name, areaId: area?.areaId, adcode: area?.adcode })
        echartObj.setOption({
          series: [
            this.getSeriesData(),
            this.getMapSeries(area?.areaId)
          ]
        })
      })
      window.addEventListener('resize', () => {
        if (echartObj && echartObj.resize) {
          echartObj.resize()
        }
      })
    },

    getOptions (areaId) {
      this.options.geo.layoutSize = areaId === '370200' ? '90%' : '70%'
      this.options.series = [this.getSeriesData(), this.getMapSeries(areaId)]
      return this.options
    },

    getSeriesData () {
      return {
        type: 'scatter',
        coordinateSystem: 'geo',
        data: this.data,
        show: true,
        symbolSize: [20, 25],
        geoIndex: 0,
        symbolOffset: [0, -20]
      }
    },

    getMapSeries () {
      // 转换数据格式为ECharts地图系列所需的格式
      const mapData = this.data ? this.data.map(item => ({
        name: item.name,
        value: Number(item.value) || 0 // 确保value是数值类型
      })) : []
      return {
        layoutCenter: ['50%', '50%'],
        layoutSize: '90%',
        name: 'map',
        type: 'map',
        map: '智慧人大',
        geoIndex: 2,
        showLegendSymbol: false,
        data: mapData,
        label: {
          show: true,
          fontSize: 14,
          position: 'center',
          color: '#fff',
          formatter: function (name) {
            return name.name.length > 6 ? name.name.substring(0, 5) + '\n' + name.name.substring(5) : name.name
          },
          emphasis: {
            show: true,
            textStyle: { color: '#fff' }
          }
        },
        roam: false,
        itemStyle: {
          borderColor: '#00B5FE',
          borderWidth: 2,
          shadowColor: 'rgba(0, 181, 254, 0.6)',
          shadowBlur: 15,
          shadowOffsetX: 0,
          shadowOffsetY: 15,
          areaColor: {
            type: 'radial',
            x: 0.4,
            y: 0.3,
            r: 0.8,
            colorStops: [
              { offset: 0, color: 'rgba(20, 80, 150, 0.7)' },
              { offset: 0.4, color: 'rgba(15, 60, 120, 0.8)' },
              { offset: 0.8, color: 'rgba(10, 40, 90, 0.9)' },
              { offset: 1, color: 'rgba(8, 25, 60, 1)' }
            ]
          }
        },
        emphasis: {
          itemStyle: {
            borderColor: '#00EAFF',
            shadowColor: 'rgba(0, 234, 255, 0.8)',
            shadowBlur: 25,
            shadowOffsetX: 0,
            shadowOffsetY: 20,
            borderWidth: 3,
            areaColor: {
              type: 'radial',
              x: 0.4,
              y: 0.3,
              r: 0.8,
              colorStops: [
                { offset: 0, color: 'rgba(40, 120, 200, 0.8)' },
                { offset: 0.4, color: 'rgba(25, 90, 160, 0.9)' },
                { offset: 0.8, color: 'rgba(15, 60, 120, 1)' },
                { offset: 1, color: 'rgba(10, 35, 80, 1)' }
              ]
            }
          }
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.map-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

#homeMap {
  width: 100%;
  height: 100%;
}
</style>
