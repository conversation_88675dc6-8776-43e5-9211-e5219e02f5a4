{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\committeeStatistics\\committeeStatisticsBox.vue?vue&type=style&index=0&id=2c47cac9&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\committeeStatistics\\committeeStatisticsBox.vue", "mtime": 1755586518266}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["committeeStatisticsBox.vue"], "names": [], "mappings": ";AAgZA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "committeeStatisticsBox.vue", "sourceRoot": "src/views/smartBrainLargeScreen/committeeStatistics", "sourcesContent": ["<template>\r\n  <div class=\"big-screen\" ref=\"bigScreen\">\r\n    <div class=\"screen-header\">\r\n      <div class=\"header-left\">\r\n        <span class=\"date-time\">{{ currentTime }}</span>\r\n        <span class=\"weather\">晴 24℃ 东南风</span>\r\n      </div>\r\n      <div class=\"header-center\">\r\n        <img src=\"../../../assets/largeScreen/top_header_txt.png\" alt=\"\" style=\"height: 50px;\">\r\n      </div>\r\n      <div class=\"header-right\"></div>\r\n    </div>\r\n    <div class=\"screen-content\">\r\n      <div class=\"left-panel\">\r\n        <!-- 委员统计 -->\r\n        <div class=\"committee_statistics\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\" @click=\"handleCommitteeClick\">委员统计</span>\r\n            <span class=\"header_text_right\">十二届二次</span>\r\n          </div>\r\n          <div class=\"committee_statistics_content\">\r\n            <div class=\"committee_statistics_num\">\r\n              <div v-for=\"(item, index) in committeeStatisticsNum\" :key=\"index\" class=\"num_box\">\r\n                <img :src=\"item.icon\" alt=\"\" class=\"num_icon\">\r\n                <div>\r\n                  <div class=\"num_label\">{{ item.label }}</div>\r\n                  <div class=\"num_value\" :style=\"`color:${item.color}`\">{{ item.value }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"committee_statistics_chart\">\r\n              <BarScrollChart id=\"committee-statistics\" :chart-data=\"committeeBarData\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- 提案统计 -->\r\n        <div class=\"proposal_statistics\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">提案统计</span>\r\n            <span class=\"header_text_right\">十二届二次会议</span>\r\n            <span class=\"header_text_center\">提交提案总数：<span>873</span>件</span>\r\n          </div>\r\n          <div class=\"proposal_statistics_content\">\r\n            <div class=\"proposal_statistics_num\">\r\n              <div v-for=\"(item, index) in proposalStatisticsNum\" :key=\"index\" class=\"num_box\">\r\n                <img :src=\"item.icon\" alt=\"\" class=\"num_icon\">\r\n                <div>\r\n                  <div class=\"num_label\">{{ item.label }}</div>\r\n                  <div class=\"num_value\" :style=\"`color:${item.color}`\">{{ item.value }}<span class=\"num_unit\">{{\r\n                    item.unit }}</span></div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"proposal_statistics_chart\">\r\n              <PieChart id=\"proposal-statistics\" :chart-data=\"proposalChartData\" :name=\"proposalChartName\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- 工作动态 -->\r\n        <div class=\"work_dynamics\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">工作动态</span>\r\n            <span class=\"header_text_right\">本年</span>\r\n          </div>\r\n          <div class=\"work_dynamics_content\">\r\n            <div class=\"dynamics-list\">\r\n              <div v-for=\"(item, index) in workDynamicsData\" :key=\"item.id\" class=\"dynamics-item\"\r\n                :class=\"{ 'with-bg-image': index % 2 === 0, 'with-bg-color': index % 2 === 1 }\">\r\n                <div class=\"dynamics-content\">\r\n                  <div class=\"dynamics-title\">{{ item.title }}</div>\r\n                  <div class=\"dynamics-date\">{{ item.date }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"center-panel\">\r\n        <!-- 地图 -->\r\n        <div class=\"map_box\">\r\n          <MapComponent :data=\"mapData\" :areaId=\"areaId + ''\" :areaName=\"areaName\" @region-click=\"handleRegionClick\" />\r\n        </div>\r\n        <!-- 履职统计 -->\r\n        <div class=\"performance_statistics\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">履职统计</span>\r\n            <span class=\"header_text_right\">十二届二次</span>\r\n          </div>\r\n          <div class=\"performance_statistics_content\">\r\n            <div class=\"table-container\">\r\n              <!-- 固定表头 -->\r\n              <div class=\"table-header\">\r\n                <div class=\"header-cell\">姓名</div>\r\n                <div class=\"header-cell\">会议活动</div>\r\n                <div class=\"header-cell\">政协提案</div>\r\n                <div class=\"header-cell\">社情民意</div>\r\n                <div class=\"header-cell\">议政建言</div>\r\n                <div class=\"header-cell\">读书心得</div>\r\n                <div class=\"header-cell\">委员培训</div>\r\n                <div class=\"header-cell\"></div> <!-- 滚动条占位 -->\r\n              </div>\r\n              <!-- 可滚动内容 -->\r\n              <div class=\"table-body\">\r\n                <div class=\"table-row\" v-for=\"(item, index) in performanceData\" :key=\"index\">\r\n                  <div class=\"table-cell name-col\">{{ item.name }}</div>\r\n                  <div class=\"table-cell meeting-col\">{{ item.meeting }}</div>\r\n                  <div class=\"table-cell proposal-col\">{{ item.proposal }}</div>\r\n                  <div class=\"table-cell opinion-col\">{{ item.opinion }}</div>\r\n                  <div class=\"table-cell suggestion-col\">{{ item.suggestion }}\r\n                  </div>\r\n                  <div class=\"table-cell reading-col\">{{ item.reading }}</div>\r\n                  <div class=\"table-cell training-col\">{{ item.training }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"right-panel\">\r\n        <!-- 社情民意 -->\r\n        <div class=\"social\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">社情民意</span>\r\n            <span class=\"header_text_right\">本年</span>\r\n          </div>\r\n          <div class=\"social_content\">\r\n            <div class=\"social-data-container\">\r\n              <div class=\"left-data-item\">\r\n                <div class=\"left-data-label\">委员报送</div>\r\n                <div class=\"left-data-value\">总数<span>{{ socialData.memberSubmit.count }}</span>篇</div>\r\n                <div class=\"left-data-detail\">采用<span>{{ socialData.memberSubmit.adopted }}</span> 篇</div>\r\n              </div>\r\n              <div class=\"center-chart\">\r\n                <div class=\"progress-content\">\r\n                  <div class=\"total-number\">{{ socialData.total }}</div>\r\n                  <div class=\"total-label\">总数</div>\r\n                </div>\r\n              </div>\r\n              <div class=\"right-data-item\">\r\n                <div class=\"right-data-label\">单位报送</div>\r\n                <div class=\"right-data-value\">总数<span>{{ socialData.unitSubmit.count }}</span>篇</div>\r\n                <div class=\"right-data-detail\">采用<span>{{ socialData.unitSubmit.adopted }}</span> 篇</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- 会议活动 -->\r\n        <div class=\"conference_activities\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">会议活动</span>\r\n            <span class=\"header_text_right\">本年</span>\r\n          </div>\r\n          <div class=\"conference_activities_content\">\r\n            <div class=\"activities-grid\">\r\n              <div v-for=\"(item, index) in conferenceActivitiesData\" :key=\"index\" class=\"activity-item\"\r\n                :class=\"getItemClass(item.name)\">\r\n                <div class=\"activity-value\">{{ item.value }}</div>\r\n                <div class=\"activity-name\">{{ item.name }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- 网络议政 -->\r\n        <div class=\"discussions\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">网络议政</span>\r\n            <span class=\"header_text_right\"></span>\r\n          </div>\r\n          <div class=\"discussions_content\">\r\n            <!-- 统计数据区域 -->\r\n            <div class=\"statistics-section\">\r\n              <div v-for=\"(item, index) in discussionsData.statistics\" :key=\"index\" class=\"stat-item\">\r\n                <div class=\"stat-dot\"></div>\r\n                <div class=\"stat-info\">\r\n                  <span class=\"stat-name\">{{ item.name }}</span>\r\n                  <span class=\"stat-value\">{{ item.value }}</span>\r\n                  <span class=\"stat-unit\">{{ item.unit }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 最热话题区域 -->\r\n            <div class=\"hot-topics-section\">\r\n              <div class=\"hot-topics-header\">\r\n                <img src=\"../../../assets/largeScreen/icon_hot.png\" alt=\"热门\" class=\"hot-icon\">\r\n                <span class=\"hot-title\">最热话题</span>\r\n              </div>\r\n              <div class=\"topics-list\">\r\n                <div v-for=\"(topic, index) in discussionsData.hotTopics\" :key=\"index\" class=\"topic-item\">\r\n                  <div class=\"topic-dot\"></div>\r\n                  <span class=\"topic-text\">{{ topic }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { useIndex } from '../screen.js'\r\nimport MapComponent from '../components/MapComponent.vue'\r\nimport PieChart from '../components/PieChart.vue'\r\nimport BarScrollChart from '../components/BarScrollChart.vue'\r\n\r\nexport default {\r\n  name: 'BigScreen',\r\n  components: {\r\n    MapComponent,\r\n    PieChart,\r\n    BarScrollChart\r\n  },\r\n  data () {\r\n    return {\r\n      currentTime: '',\r\n      // 委员统计\r\n      committeeStatisticsNum: [\r\n        { icon: require('../../../assets/largeScreen/icon_cppcc_member.png'), label: '政协委员（人）', value: '10095', color: '#ffffff' },\r\n        { icon: require('../../../assets/largeScreen/icon_committee_member.png'), label: '政协常委', value: '8742', color: '#FCD603' }\r\n      ],\r\n      // 委员统计柱状图数据\r\n      committeeBarData: [\r\n        { name: '中共', value: 32 },\r\n        { name: '民革', value: 15 },\r\n        { name: '民盟', value: 14 },\r\n        { name: '民建', value: 13 },\r\n        { name: '民进', value: 12 },\r\n        { name: '农工', value: 10 },\r\n        { name: '致公', value: 8 },\r\n        { name: '九三', value: 7 },\r\n        { name: '台盟', value: 6 },\r\n        { name: '无党派', value: 5 }\r\n      ],\r\n      // 提案统计\r\n      proposalStatisticsNum: [\r\n        { icon: require('../../../assets/largeScreen/icon_committee_proposal.png'), label: '委员提案', value: '456', unit: '件', color: '#0DBCDB' },\r\n        { icon: require('../../../assets/largeScreen/icon_circles_proposal.png'), label: '界别提案', value: '354', unit: '件', color: '#0058FF' },\r\n        { icon: require('../../../assets/largeScreen/icon_committee_proposal.png'), label: '组织提案', value: '211', unit: '件', color: '#1AEBDD' }\r\n      ],\r\n      // 提案统计图表数据\r\n      proposalChartData: [\r\n        { name: '政府制约', value: 22.52 },\r\n        { name: '县区市政', value: 18.33 },\r\n        { name: '司法法治', value: 15.73 },\r\n        { name: '区市政府', value: 11.34 },\r\n        { name: '科技工商', value: 9.56 },\r\n        { name: '教育文化', value: 8.09 },\r\n        { name: '派出机构', value: 4.21 },\r\n        { name: '社会事业', value: 3.71 },\r\n        { name: '企事业', value: 3.65 },\r\n        { name: '农村卫生', value: 3.21 },\r\n        { name: '其他机构', value: 1.86 },\r\n        { name: '各群体他', value: 1.02 }\r\n      ],\r\n      proposalChartName: '提案统计',\r\n      // 工作动态数据\r\n      workDynamicsData: [\r\n        {\r\n          id: 1,\r\n          title: '市政协社会和法制工作办公室围绕\"居家适老化改造\"开展专题调研',\r\n          date: '2025-06-03'\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '\"与民同行 共创共赢\"新格局下民营企业转型发展座谈会召开',\r\n          date: '2025-05-30'\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '\"惠民生·基层行\"义诊活动温暖人心',\r\n          date: '2025-05-30'\r\n        },\r\n        {\r\n          id: 4,\r\n          title: '市科技局面复市政协科技界别提案',\r\n          date: '2025-05-30'\r\n        },\r\n        {\r\n          id: 5,\r\n          title: '市政协召开\"推进数字化转型\"专题协商会',\r\n          date: '2025-05-28'\r\n        },\r\n        {\r\n          id: 6,\r\n          title: '政协委员深入基层开展\"三服务\"活动',\r\n          date: '2025-05-25'\r\n        }\r\n      ],\r\n      // 履职统计数据\r\n      performanceData: [\r\n        { name: '马平安', meeting: 515, proposal: 15, opinion: 0, suggestion: 0, reading: 0, training: 0 },\r\n        { name: '马波', meeting: 400, proposal: 0, opinion: 0, suggestion: 12, reading: 0, training: 15 },\r\n        { name: '王玉民', meeting: 490, proposal: 1, opinion: 2, suggestion: 0, reading: 4, training: 25 },\r\n        { name: '王俊宝', meeting: 500, proposal: 0, opinion: 4, suggestion: 1, reading: 5, training: 60 },\r\n        { name: '李明', meeting: 320, proposal: 8, opinion: 1, suggestion: 3, reading: 2, training: 18 },\r\n        { name: '张华', meeting: 280, proposal: 5, opinion: 0, suggestion: 2, reading: 1, training: 12 },\r\n        { name: '刘强', meeting: 450, proposal: 3, opinion: 6, suggestion: 0, reading: 3, training: 35 },\r\n        { name: '陈静', meeting: 380, proposal: 2, opinion: 3, suggestion: 4, reading: 6, training: 28 }\r\n      ],\r\n      // 社情民意数据\r\n      socialData: {\r\n        memberSubmit: {\r\n          count: 345,\r\n          adopted: 21\r\n        },\r\n        unitSubmit: {\r\n          count: 547,\r\n          adopted: 79\r\n        },\r\n        total: 1057\r\n      },\r\n      // 会议活动数据\r\n      conferenceActivitiesData: [\r\n        { name: '会议次数', value: 201 },\r\n        { name: '活动次数', value: 310 },\r\n        { name: '会议人数', value: 2412 },\r\n        { name: '活动人数', value: 4015 }\r\n      ],\r\n      // 网络议政数据\r\n      discussionsData: {\r\n        statistics: [\r\n          { name: '发布议题', value: 72, unit: '个' },\r\n          { name: '累计参与人次', value: 39301, unit: '次' },\r\n          { name: '累计征求意见', value: 12306, unit: '条' }\r\n        ],\r\n        hotTopics: [\r\n          '推进黄河国家文化公园建设',\r\n          '持续推进黄河流域生态保护修复，助力\"先行区\"建设',\r\n          '全面加强新时代中小学劳动教育'\r\n        ]\r\n      },\r\n      mapData: [\r\n        { name: '市南区', value: '1200', areaId: '370202', adcode: 370202 },\r\n        { name: '市北区', value: '1300', areaId: '370203', adcode: 370203 },\r\n        { name: '黄岛区', value: '850', areaId: '370211', adcode: 370211 },\r\n        { name: '崂山区', value: '700', areaId: '370212', adcode: 370212 },\r\n        { name: '李沧区', value: '1000', areaId: '370213', adcode: 370213 },\r\n        { name: '城阳区', value: '1100', areaId: '370214', adcode: 370214 },\r\n        { name: '即墨区', value: '950', areaId: '370215', adcode: 370215 },\r\n        { name: '胶州市', value: '800', areaId: '370281', adcode: 370281 },\r\n        { name: '平度市', value: '1400', areaId: '370283', adcode: 370283 },\r\n        { name: '莱西市', value: '600', areaId: '370285', adcode: 370285 }\r\n      ],\r\n      areaId: JSON.parse(sessionStorage.getItem('areaId' + this.$logo())),\r\n      areaName: '青岛市'\r\n    }\r\n  },\r\n  computed: {\r\n  },\r\n  mounted () {\r\n    this.initScreen()\r\n    this.updateTime()\r\n    this.timeInterval = setInterval(this.updateTime, 1000)\r\n  },\r\n  beforeDestroy () {\r\n    if (this.timeInterval) {\r\n      clearInterval(this.timeInterval)\r\n    }\r\n  },\r\n  methods: {\r\n    initScreen () {\r\n      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)\r\n      calcRate()\r\n      windowDraw()\r\n    },\r\n    updateTime () {\r\n      const now = new Date()\r\n      this.currentTime = now.toLocaleString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit',\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        second: '2-digit'\r\n      })\r\n    },\r\n    getItemClass (name) {\r\n      if (name.includes('会议')) {\r\n        return 'meeting-item'\r\n      } else if (name.includes('活动')) {\r\n        return 'activity-item-bg'\r\n      }\r\n      return ''\r\n    },\r\n    handleRegionClick (region) {\r\n      console.log('选中地区:', region)\r\n      // 这里可以添加地区点击后的业务逻辑\r\n      // 比如显示该地区的详细数据等\r\n    },\r\n    // 打开委员统计\r\n    handleCommitteeClick () {\r\n      this.$router.push({ path: '/committeeStatisticsBox', query: { route: '/committeeStatisticsBox' } })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.big-screen {\r\n  width: 1920px;\r\n  height: 1080px;\r\n  position: relative;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  transform-origin: left top;\r\n  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;\r\n  background-size: cover;\r\n  background-position: center;\r\n\r\n  .screen-header {\r\n    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;\r\n    background-size: 100% 100%;\r\n    background-position: center;\r\n    height: 65px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 40px;\r\n\r\n    .header-left {\r\n      display: flex;\r\n      gap: 20px;\r\n      font-size: 14px;\r\n      color: #8cc8ff;\r\n      flex: 1;\r\n    }\r\n\r\n    .header-center {\r\n      width: 60%;\r\n      text-align: center;\r\n    }\r\n\r\n    .header-right {\r\n      flex: 1;\r\n    }\r\n  }\r\n\r\n  .screen-content {\r\n    height: calc(100% - 65px);\r\n    display: flex;\r\n    padding: 35px 20px 0 20px;\r\n    gap: 30px;\r\n\r\n    .header_box {\r\n      position: absolute;\r\n      top: 15px;\r\n      left: 24px;\r\n      right: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n\r\n      .header_text_left {\r\n        font-weight: bold;\r\n        font-size: 20px;\r\n        color: #FFFFFF;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .header_text_right {\r\n        font-size: 15px;\r\n        color: #FFD600;\r\n      }\r\n\r\n      .header_text_center {\r\n        font-size: 15px;\r\n        color: #FFFFFF;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          font-weight: 500;\r\n          font-size: 24px;\r\n          color: #02FBFB;\r\n          margin: 0 10px 0 6px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .left-panel,\r\n    .right-panel {\r\n      width: 470px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 20px 30px;\r\n    }\r\n\r\n    .left-panel {\r\n      .committee_statistics {\r\n        background: url('../../../assets/largeScreen/committee_statistics_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 320px;\r\n        width: 100%;\r\n\r\n        .committee_statistics_content {\r\n          height: 100%;\r\n          margin-top: 72px;\r\n          margin-left: 20px;\r\n          margin-right: 20px;\r\n\r\n          .committee_statistics_num {\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-around;\r\n\r\n            .num_box {\r\n              display: flex;\r\n              align-items: center;\r\n\r\n              .num_icon {\r\n                width: 64px;\r\n                height: 64px;\r\n                margin-right: 14px;\r\n              }\r\n\r\n              .num_label {\r\n                font-size: 15px;\r\n                color: #B4C0CC;\r\n                margin-bottom: 14px;\r\n              }\r\n\r\n              .num_value {\r\n                font-weight: bold;\r\n                font-size: 26px;\r\n                color: #FFFFFF;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .proposal_statistics {\r\n        background: url('../../../assets/largeScreen/proposal_statistics_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 310px;\r\n        width: 100%;\r\n\r\n        .proposal_statistics_content {\r\n          height: 100%;\r\n          margin-top: 72px;\r\n          margin-left: 20px;\r\n          margin-right: 20px;\r\n\r\n          .proposal_statistics_num {\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n\r\n            .num_box {\r\n              display: flex;\r\n              align-items: center;\r\n\r\n              .num_icon {\r\n                width: 54px;\r\n                height: 54px;\r\n                margin-right: 10px;\r\n              }\r\n\r\n              .num_label {\r\n                font-size: 14px;\r\n                color: #FFFFFF;\r\n                margin-bottom: 5px;\r\n              }\r\n\r\n              .num_value {\r\n                font-size: 20px;\r\n                color: #0DBCDB;\r\n                font-weight: 500;\r\n\r\n                .num_unit {\r\n                  font-size: 14px;\r\n                  color: #FFFFFF;\r\n                  font-weight: normal;\r\n                  margin-left: 4px;\r\n                }\r\n              }\r\n            }\r\n          }\r\n\r\n          .proposal_statistics_chart {\r\n            height: 180px;\r\n            margin-top: 20px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .work_dynamics {\r\n        background: url('../../../assets/largeScreen/work_dynamics_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 270px;\r\n        width: 100%;\r\n\r\n        .work_dynamics_content {\r\n          height: 100%;\r\n          margin-top: 65px;\r\n          margin-left: 14px;\r\n          margin-right: 14px;\r\n\r\n          .dynamics-list {\r\n            height: calc(100% - 70px);\r\n            overflow-y: auto;\r\n\r\n            &::-webkit-scrollbar {\r\n              width: 4px;\r\n            }\r\n\r\n            &::-webkit-scrollbar-track {\r\n              background: rgba(0, 30, 60, 0.3);\r\n              border-radius: 2px;\r\n            }\r\n\r\n            &::-webkit-scrollbar-thumb {\r\n              background: rgba(0, 212, 255, 0.4);\r\n              border-radius: 2px;\r\n\r\n              &:hover {\r\n                background: rgba(0, 212, 255, 0.6);\r\n              }\r\n            }\r\n\r\n            .dynamics-item {\r\n              margin-bottom: 12px;\r\n              overflow: hidden;\r\n              position: relative;\r\n\r\n              &:last-child {\r\n                margin-bottom: 0;\r\n              }\r\n\r\n              // 奇数项 - 背景图片样式\r\n              &.with-bg-image {\r\n                background: url('../../../assets/largeScreen/table_bg.png') no-repeat;\r\n                background-size: 100% 100%;\r\n                background-position: center;\r\n              }\r\n\r\n              // 偶数项 - 背景颜色样式\r\n              &.with-bg-color {\r\n                background: rgba(6, 79, 219, 0.05);\r\n              }\r\n\r\n              .dynamics-content {\r\n                padding: 12px 15px;\r\n                position: relative;\r\n                z-index: 2;\r\n                display: flex;\r\n                justify-content: space-between;\r\n                align-items: center;\r\n\r\n                .dynamics-title {\r\n                  flex: 1;\r\n                  color: #fff;\r\n                  font-size: 16px;\r\n                  margin-right: 16px;\r\n                  // 文本溢出处理\r\n                  display: -webkit-box;\r\n                  -webkit-line-clamp: 1;\r\n                  -webkit-box-orient: vertical;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                }\r\n\r\n                .dynamics-date {\r\n                  flex-shrink: 0;\r\n                  font-size: 16px;\r\n                  color: #FFFFFF;\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .right-panel {\r\n      .social {\r\n        background: url('../../../assets/largeScreen/social_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 290px;\r\n        width: 100%;\r\n\r\n        .social_content {\r\n          height: 190px;\r\n          margin-top: 75px;\r\n          margin-left: 12px;\r\n          margin-right: 12px;\r\n          background: url('../../../assets/largeScreen/social_content_bg.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n\r\n          .social-data-container {\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n            width: 100%;\r\n            height: 100%;\r\n\r\n            .left-data-item {\r\n              display: flex;\r\n              flex-direction: column;\r\n              align-items: center;\r\n              text-align: center;\r\n              flex: 1;\r\n              margin-right: 20px;\r\n\r\n              .left-data-label {\r\n                font-size: 14px;\r\n                color: #19ECFF;\r\n                margin-bottom: 20px;\r\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\r\n              }\r\n\r\n              .left-data-value {\r\n                font-size: 14px;\r\n                color: #FFFFFF;\r\n                margin-bottom: 15px;\r\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\r\n\r\n                span {\r\n                  font-weight: 400;\r\n                  font-size: 20px;\r\n                  color: #FFD600;\r\n                  margin: 0 5px;\r\n                }\r\n              }\r\n\r\n              .left-data-detail {\r\n                font-size: 14px;\r\n                color: #FFFFFF;\r\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\r\n\r\n                span {\r\n                  font-weight: 400;\r\n                  font-size: 20px;\r\n                  color: #19ECFF;\r\n                  margin: 0 5px;\r\n                }\r\n              }\r\n            }\r\n\r\n            .center-chart {\r\n              flex: 1;\r\n              display: flex;\r\n              justify-content: center;\r\n              align-items: center;\r\n\r\n              .progress-content {\r\n                position: absolute;\r\n                display: flex;\r\n                flex-direction: column;\r\n                align-items: center;\r\n                justify-content: center;\r\n\r\n                .total-number {\r\n                  font-weight: 500;\r\n                  font-size: 24px;\r\n                  color: #FFD600;\r\n                  margin-bottom: 8px;\r\n                }\r\n\r\n                .total-label {\r\n                  font-size: 14px;\r\n                  color: #ffffff;\r\n                }\r\n              }\r\n            }\r\n\r\n            .right-data-item {\r\n              display: flex;\r\n              flex-direction: column;\r\n              align-items: center;\r\n              text-align: center;\r\n              flex: 1;\r\n              margin-left: 20px;\r\n\r\n              .right-data-label {\r\n                font-size: 14px;\r\n                color: #19ECFF;\r\n                margin-bottom: 20px;\r\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\r\n              }\r\n\r\n              .right-data-value {\r\n                font-size: 14px;\r\n                color: #FFFFFF;\r\n                margin-bottom: 15px;\r\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\r\n\r\n                span {\r\n                  font-weight: 400;\r\n                  font-size: 20px;\r\n                  color: #FFD600;\r\n                  margin: 0 5px;\r\n                }\r\n              }\r\n\r\n              .right-data-detail {\r\n                font-size: 14px;\r\n                color: #FFFFFF;\r\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\r\n\r\n                span {\r\n                  font-weight: 400;\r\n                  font-size: 20px;\r\n                  color: #19ECFF;\r\n                  margin: 0 5px;\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .conference_activities {\r\n        background: url('../../../assets/largeScreen/conference_activities_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 290px;\r\n        width: 100%;\r\n\r\n        .conference_activities_content {\r\n          margin-top: 70px;\r\n          margin-left: 20px;\r\n          margin-right: 20px;\r\n\r\n          .activities-grid {\r\n            display: grid;\r\n            grid-template-columns: 1fr 1fr;\r\n            grid-template-rows: 1fr 1fr;\r\n            gap: 15px;\r\n            height: 100%;\r\n\r\n            .activity-item {\r\n              display: flex;\r\n              flex-direction: column;\r\n              align-items: center;\r\n              justify-content: center;\r\n              background-size: 100% 100%;\r\n              background-repeat: no-repeat;\r\n              background-position: center;\r\n              height: 92px;\r\n\r\n              .activity-value {\r\n                font-weight: 500;\r\n                font-size: 32px;\r\n                color: #FFFFFF;\r\n                line-height: 24px;\r\n                margin-bottom: 12px;\r\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\r\n              }\r\n\r\n              .activity-name {\r\n                font-size: 16px;\r\n                color: #FFFFFF;\r\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\r\n              }\r\n\r\n              &.meeting-item {\r\n                background-image: url('../../../assets/largeScreen/icon_meeting_item_bg.png');\r\n              }\r\n\r\n              &.activity-item-bg {\r\n                background-image: url('../../../assets/largeScreen/icon_activity_item_bg.png');\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .discussions {\r\n        background: url('../../../assets/largeScreen/discussions_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 320px;\r\n        width: 100%;\r\n\r\n        .discussions_content {\r\n          margin-top: 75px;\r\n          margin-left: 20px;\r\n          margin-right: 20px;\r\n          height: calc(100% - 90px);\r\n          display: flex;\r\n          flex-direction: column;\r\n\r\n          .statistics-section {\r\n            display: flex;\r\n            flex-wrap: wrap;\r\n            justify-content: space-between;\r\n            gap: 20px;\r\n            margin-bottom: 25px;\r\n\r\n            .stat-item {\r\n              display: flex;\r\n              align-items: center;\r\n              gap: 12px;\r\n\r\n              .stat-dot {\r\n                width: 10px;\r\n                height: 10px;\r\n                border-radius: 50%;\r\n                background: linear-gradient(180deg, #00EEFF 0%, #E1FDFF 100%);\r\n                flex-shrink: 0;\r\n                margin-top: 5px;\r\n              }\r\n\r\n              .stat-info {\r\n                display: flex;\r\n                align-items: center;\r\n                gap: 8px;\r\n\r\n                .stat-name {\r\n                  font-size: 15px;\r\n                  color: #FFFFFF;\r\n                  font-family: DIN-BoldItalic, DIN-BoldItalic;\r\n                }\r\n\r\n                .stat-value {\r\n                  font-weight: 500;\r\n                  font-size: 20px;\r\n                  color: #FFD600;\r\n                  font-family: DIN-BoldItalic, DIN-BoldItalic;\r\n                }\r\n\r\n                .stat-unit {\r\n                  font-size: 15px;\r\n                  color: #FFFFFF;\r\n                  font-family: DIN-BoldItalic, DIN-BoldItalic;\r\n                }\r\n              }\r\n            }\r\n          }\r\n\r\n          .hot-topics-section {\r\n            flex: 1;\r\n            background: rgba(31, 198, 255, 0.16);\r\n            padding: 12px 16px;\r\n\r\n            .hot-topics-header {\r\n              display: flex;\r\n              align-items: center;\r\n              gap: 8px;\r\n              margin-bottom: 15px;\r\n\r\n              .hot-icon {\r\n                width: 20px;\r\n                height: 20px;\r\n              }\r\n\r\n              .hot-title {\r\n                font-size: 16px;\r\n                color: #02FBFB;\r\n                font-weight: 500;\r\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\r\n              }\r\n            }\r\n\r\n            .topics-list {\r\n              display: flex;\r\n              flex-direction: column;\r\n              gap: 12px;\r\n\r\n              .topic-item {\r\n                display: flex;\r\n                align-items: flex-start;\r\n                gap: 10px;\r\n\r\n                .topic-dot {\r\n                  width: 6px;\r\n                  height: 6px;\r\n                  border-radius: 50%;\r\n                  background: rgba(217, 217, 217, 0.5);\r\n                  margin-top: 8px;\r\n                  flex-shrink: 0;\r\n                }\r\n\r\n                .topic-text {\r\n                  font-size: 14px;\r\n                  color: #FFFFFF;\r\n                  line-height: 22px;\r\n                  font-family: DIN-BoldItalic, DIN-BoldItalic;\r\n                  text-overflow: ellipsis;\r\n                  overflow: hidden;\r\n                  white-space: nowrap;\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .center-panel {\r\n      flex: 1;\r\n      gap: 20px;\r\n      display: flex;\r\n      flex-direction: column;\r\n\r\n      .map_box {\r\n        background: url('../../../assets/largeScreen/map_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        height: 650px;\r\n        width: 100%;\r\n        position: relative;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n      }\r\n\r\n      .performance_statistics {\r\n        background: url('../../../assets/largeScreen/performance_statistics_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 270px;\r\n        width: 100%;\r\n\r\n        .performance_statistics_content {\r\n          height: 100%;\r\n          margin-top: 65px;\r\n          margin-left: 16px;\r\n          margin-right: 16px;\r\n\r\n          .table-container {\r\n            height: calc(100% - 75px);\r\n            display: flex;\r\n            flex-direction: column;\r\n            border: 1px solid rgba(0, 212, 255, 0.2);\r\n            overflow: hidden;\r\n            /* 使用CSS Grid确保列对齐 */\r\n            --name-col-width: 120px;\r\n            --scrollbar-width: 6px;\r\n\r\n            .table-header {\r\n              display: grid;\r\n              grid-template-columns: var(--name-col-width) repeat(6, 1fr) var(--scrollbar-width);\r\n              border-bottom: 1px solid rgba(0, 212, 255, 0.4);\r\n              position: sticky;\r\n              top: 0;\r\n              z-index: 10;\r\n\r\n              .header-cell {\r\n                padding: 12px 8px;\r\n                text-align: center;\r\n                color: #E6F7FF;\r\n                font-size: 15px;\r\n                border-right: 1px solid rgba(0, 212, 255, 0.3);\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n\r\n                &:last-child {\r\n                  border-right: none;\r\n                  background: transparent;\r\n                  border: none;\r\n                }\r\n\r\n                // &.name-col {\r\n                //   background: rgba(0, 100, 180, 0.9);\r\n                //   font-weight: 600;\r\n                // }\r\n              }\r\n            }\r\n\r\n            .table-body {\r\n              flex: 1;\r\n              overflow-y: auto;\r\n\r\n              &::-webkit-scrollbar {\r\n                width: 6px;\r\n              }\r\n\r\n              &::-webkit-scrollbar-track {\r\n                background: rgba(0, 30, 60, 0.3);\r\n                border-radius: 3px;\r\n              }\r\n\r\n              &::-webkit-scrollbar-thumb {\r\n                background: rgba(0, 212, 255, 0.4);\r\n                border-radius: 3px;\r\n\r\n                &:hover {\r\n                  background: rgba(0, 212, 255, 0.6);\r\n                }\r\n              }\r\n\r\n              .table-row {\r\n                display: grid;\r\n                grid-template-columns: var(--name-col-width) repeat(6, 1fr);\r\n                border-bottom: 1px solid rgba(0, 212, 255, 0.4);\r\n                transition: all 0.3s ease;\r\n\r\n                &:hover {\r\n                  background: rgba(0, 212, 255, 0.1);\r\n                }\r\n\r\n                .table-cell {\r\n                  padding: 12px 8px;\r\n                  text-align: center;\r\n                  color: #FFFFFF;\r\n                  font-size: 14px;\r\n                  border-right: 1px solid rgba(0, 212, 255, 0.4);\r\n                  transition: all 0.3s ease;\r\n                  display: flex;\r\n                  align-items: center;\r\n                  justify-content: center;\r\n\r\n                  &:last-child {\r\n                    border-right: none;\r\n                  }\r\n\r\n                  &.name-col {\r\n                    background: rgba(0, 60, 120, 0.4);\r\n                    color: #FFF;\r\n                    font-weight: 500;\r\n                  }\r\n\r\n                  &.meeting-col {\r\n                    background: rgba(10, 63, 111, 0.4);\r\n                    color: #59F7CA;\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                  }\r\n\r\n                  &.proposal-col {\r\n                    background: rgba(10, 63, 111, 0.4);\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    color: #00FFF7;\r\n                  }\r\n\r\n                  &.opinion-col {\r\n                    background: rgba(10, 63, 111, 0.4);\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    color: #FF386B;\r\n                  }\r\n\r\n                  &.suggestion-col {\r\n                    background: rgba(10, 63, 111, 0.4);\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    color: #81C4E4;\r\n                  }\r\n\r\n                  &.reading-col {\r\n                    background: rgba(10, 63, 111, 0.4);\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    color: #387BFD;\r\n                  }\r\n\r\n                  &.training-col {\r\n                    background: rgba(10, 63, 111, 0.4);\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    color: #FF911F;\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}