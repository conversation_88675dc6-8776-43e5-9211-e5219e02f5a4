{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\committeeStatistics\\committeeStatisticsBox.vue?vue&type=style&index=0&id=2c47cac9&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\committeeStatistics\\committeeStatisticsBox.vue", "mtime": 1755587391590}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouYmlnLXNjcmVlbiB7DQogIHdpZHRoOiAxOTIwcHg7DQogIGhlaWdodDogMTA4MHB4Ow0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIHRvcDogNTAlOw0KICBsZWZ0OiA1MCU7DQogIHRyYW5zZm9ybTogdHJhbnNsYXRlKC01MCUsIC01MCUpOw0KICB0cmFuc2Zvcm0tb3JpZ2luOiBsZWZ0IHRvcDsNCiAgYmFja2dyb3VuZDogdXJsKCcuLi8uLi8uLi9hc3NldHMvbGFyZ2VTY3JlZW4vYmcuanBnJykgbm8tcmVwZWF0Ow0KICBiYWNrZ3JvdW5kLXNpemU6IGNvdmVyOw0KICBiYWNrZ3JvdW5kLXBvc2l0aW9uOiBjZW50ZXI7DQoNCiAgLnNjcmVlbi1oZWFkZXIgew0KICAgIGJhY2tncm91bmQ6IHVybCgnLi4vLi4vLi4vYXNzZXRzL2xhcmdlU2NyZWVuL3RvcF9oZWFkZXJfYmcucG5nJykgbm8tcmVwZWF0Ow0KICAgIGJhY2tncm91bmQtc2l6ZTogMTAwJSAxMDAlOw0KICAgIGJhY2tncm91bmQtcG9zaXRpb246IGNlbnRlcjsNCiAgICBoZWlnaHQ6IDY1cHg7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIHBhZGRpbmc6IDAgNDBweDsNCg0KICAgIC5oZWFkZXItbGVmdCB7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgZ2FwOiAyMHB4Ow0KICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgY29sb3I6ICM4Y2M4ZmY7DQogICAgICBmbGV4OiAxOw0KICAgIH0NCg0KICAgIC5oZWFkZXItY2VudGVyIHsNCiAgICAgIHdpZHRoOiA2MCU7DQogICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgfQ0KDQogICAgLmhlYWRlci1yaWdodCB7DQogICAgICBmbGV4OiAxOw0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7DQogICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KDQogICAgICAuaGVhZGVyLWJ1dHRvbnMgew0KICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICBnYXA6IDE1cHg7DQoNCiAgICAgICAgLmhlYWRlci1idG4gew0KICAgICAgICAgIHBhZGRpbmc6IDhweCAyMHB4Ow0KICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICAgIGNvbG9yOiAjRkZGRkZGOw0KICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHJnYmEoMCwgMTgxLCAyNTQsIDAuOCkgMCUsIHJnYmEoMCwgMTAwLCAyMDAsIDAuOCkgMTAwJSk7DQogICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgwLCAxODEsIDI1NCwgMC42KTsNCiAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOw0KICAgICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgICAgICAgICBvdmVyZmxvdzogaGlkZGVuOw0KDQogICAgICAgICAgJjo6YmVmb3JlIHsNCiAgICAgICAgICAgIGNvbnRlbnQ6ICcnOw0KICAgICAgICAgICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgICAgICAgICAgdG9wOiAwOw0KICAgICAgICAgICAgbGVmdDogLTEwMCU7DQogICAgICAgICAgICB3aWR0aDogMTAwJTsNCiAgICAgICAgICAgIGhlaWdodDogMTAwJTsNCiAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgdHJhbnNwYXJlbnQsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKSwgdHJhbnNwYXJlbnQpOw0KICAgICAgICAgICAgdHJhbnNpdGlvbjogbGVmdCAwLjVzOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgICY6aG92ZXIgew0KICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgcmdiYSgwLCAxODEsIDI1NCwgMSkgMCUsIHJnYmEoMCwgMTIwLCAyMjAsIDEpIDEwMCUpOw0KICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiByZ2JhKDAsIDE4MSwgMjU0LCAxKTsNCiAgICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTsNCiAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgwLCAxODEsIDI1NCwgMC40KTsNCg0KICAgICAgICAgICAgJjo6YmVmb3JlIHsNCiAgICAgICAgICAgICAgbGVmdDogMTAwJTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQoNCiAgICAgICAgICAmLmN1cnJlbnQtbW9kdWxlLWJ0biB7DQogICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCByZ2JhKDI1NSwgMjE0LCAwLCAwLjkpIDAlLCByZ2JhKDI1NSwgMTY1LCAwLCAwLjkpIDEwMCUpOw0KICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiByZ2JhKDI1NSwgMjE0LCAwLCAwLjgpOw0KICAgICAgICAgICAgY29sb3I6ICMzMzM7DQoNCiAgICAgICAgICAgICY6aG92ZXIgew0KICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCByZ2JhKDI1NSwgMjE0LCAwLCAxKSAwJSwgcmdiYSgyNTUsIDE2NSwgMCwgMSkgMTAwJSk7DQogICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogcmdiYSgyNTUsIDIxNCwgMCwgMSk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KDQogICAgICAgICAgc3BhbiB7DQogICAgICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7DQogICAgICAgICAgICB6LWluZGV4OiAxOw0KICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCg0KICAuc2NyZWVuLWNvbnRlbnQgew0KICAgIGhlaWdodDogY2FsYygxMDAlIC0gNjVweCk7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBwYWRkaW5nOiAyMHB4Ow0KICAgIGdhcDogMjBweDsNCg0KICAgIC5oZWFkZXJfYm94IHsNCiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICAgIHRvcDogMTVweDsNCiAgICAgIGxlZnQ6IDI0cHg7DQogICAgICByaWdodDogMDsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KDQogICAgICAuaGVhZGVyX3RleHRfbGVmdCB7DQogICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICAgICAgICBmb250LXNpemU6IDIwcHg7DQogICAgICAgIGNvbG9yOiAjRkZGRkZGOw0KICAgICAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgICB9DQoNCiAgICAgIC5oZWFkZXJfdGV4dF9yaWdodCB7DQogICAgICAgIGZvbnQtc2l6ZTogMTVweDsNCiAgICAgICAgY29sb3I6ICNGRkQ2MDA7DQogICAgICB9DQoNCiAgICAgIC5oZWFkZXJfdGV4dF9jZW50ZXIgew0KICAgICAgICBmb250LXNpemU6IDE1cHg7DQogICAgICAgIGNvbG9yOiAjRkZGRkZGOw0KICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KDQogICAgICAgIHNwYW4gew0KICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7DQogICAgICAgICAgZm9udC1zaXplOiAyNHB4Ow0KICAgICAgICAgIGNvbG9yOiAjMDJGQkZCOw0KICAgICAgICAgIG1hcmdpbjogMCAxMHB4IDAgNnB4Ow0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KDQogICAgLmxlZnQtcGFuZWwgew0KICAgICAgd2lkdGg6IDYwJTsNCiAgICAgIGRpc3BsYXk6IGdyaWQ7DQogICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmciAxZnI7DQogICAgICBncmlkLXRlbXBsYXRlLXJvd3M6IGF1dG8gYXV0byBhdXRvOw0KICAgICAgZ2FwOiAyMHB4Ow0KICAgICAgaGVpZ2h0OiAxMDAlOw0KICAgIH0NCg0KICAgIC5yaWdodC1wYW5lbCB7DQogICAgICB3aWR0aDogNDAlOw0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgICBnYXA6IDIwcHg7DQogICAgfQ0KDQogICAgLy8g5bem5L6n6Z2i5p2/5qC35byPDQogICAgLmxlZnQtcGFuZWwgew0KDQogICAgICAvLyDlp5TlkZjmlbDph48NCiAgICAgIC5jb21taXR0ZWUtY291bnQtc2VjdGlvbiB7DQogICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHJnYmEoMCwgNTAsIDEwMCwgMC44KSAwJSwgcmdiYSgwLCAzMCwgNjAsIDAuOSkgMTAwJSk7DQogICAgICAgIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMCwgMTgxLCAyNTQsIDAuMyk7DQogICAgICAgIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlOw0KICAgICAgICBwYWRkaW5nOiAyMHB4Ow0KICAgICAgICBoZWlnaHQ6IDIwMHB4Ow0KDQogICAgICAgIC5jb3VudC1jb250ZW50IHsNCiAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYXJvdW5kOw0KICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgICAgaGVpZ2h0OiAxMDAlOw0KICAgICAgICAgIG1hcmdpbi10b3A6IDMwcHg7DQoNCiAgICAgICAgICAuY291bnQtaXRlbSB7DQogICAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQoNCiAgICAgICAgICAgIC5jb3VudC12YWx1ZSB7DQogICAgICAgICAgICAgIGZvbnQtc2l6ZTogNDhweDsNCiAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgICAgICAgICAgIGNvbG9yOiAjMDBENEZGOw0KICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KICAgICAgICAgICAgICB0ZXh0LXNoYWRvdzogMCAwIDEwcHggcmdiYSgwLCAyMTIsIDI1NSwgMC41KTsNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgLmNvdW50LWxhYmVsIHsNCiAgICAgICAgICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgICAgICAgICAgICBjb2xvcjogI0ZGRkZGRjsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgLy8g5oCn5Yir5q+U5L6LDQogICAgICAuZ2VuZGVyLXJhdGlvLXNlY3Rpb24gew0KICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCByZ2JhKDAsIDUwLCAxMDAsIDAuOCkgMCUsIHJnYmEoMCwgMzAsIDYwLCAwLjkpIDEwMCUpOw0KICAgICAgICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDAsIDE4MSwgMjU0LCAwLjMpOw0KICAgICAgICBib3JkZXItcmFkaXVzOiA4cHg7DQogICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgICAgICAgcGFkZGluZzogMjBweDsNCiAgICAgICAgaGVpZ2h0OiAyMDBweDsNCg0KICAgICAgICAuZ2VuZGVyLWNvbnRlbnQgew0KICAgICAgICAgIG1hcmdpbi10b3A6IDMwcHg7DQogICAgICAgICAgaGVpZ2h0OiAxMDAlOw0KDQogICAgICAgICAgLmdlbmRlci1jaGFydCB7DQogICAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1hcm91bmQ7DQogICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICAgICAgaGVpZ2h0OiAxMDAlOw0KDQogICAgICAgICAgICAucmF0aW8taXRlbSB7DQogICAgICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCg0KICAgICAgICAgICAgICAucmF0aW8tY2lyY2xlIHsNCiAgICAgICAgICAgICAgICB3aWR0aDogODBweDsNCiAgICAgICAgICAgICAgICBoZWlnaHQ6IDgwcHg7DQogICAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogNTAlOw0KICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgICAgICAgICAgICAgICBmb250LXNpemU6IDE4cHg7DQogICAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgICAgICAgICAgICAgY29sb3I6ICNGRkZGRkY7DQogICAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMTBweDsNCg0KICAgICAgICAgICAgICAgICYubWFsZSB7DQogICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBjb25pYy1ncmFkaWVudCgjMDBENEZGIDAlIDcwJSwgcmdiYSgwLCAyMTIsIDI1NSwgMC4yKSA3MCUgMTAwJSk7DQogICAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgICAgJi5mZW1hbGUgew0KICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogY29uaWMtZ3JhZGllbnQoI0ZGRDYwMCAwJSAzMCUsIHJnYmEoMjU1LCAyMTQsIDAsIDAuMikgMzAlIDEwMCUpOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAvLyDlubTpvoQNCiAgICAgIC5hZ2Utc2VjdGlvbiB7DQogICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHJnYmEoMCwgNTAsIDEwMCwgMC44KSAwJSwgcmdiYSgwLCAzMCwgNjAsIDAuOSkgMTAwJSk7DQogICAgICAgIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMCwgMTgxLCAyNTQsIDAuMyk7DQogICAgICAgIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlOw0KICAgICAgICBwYWRkaW5nOiAyMHB4Ow0KICAgICAgICBoZWlnaHQ6IDI1MHB4Ow0KDQogICAgICAgIC5hZ2UtY29udGVudCB7DQogICAgICAgICAgbWFyZ2luLXRvcDogMzBweDsNCiAgICAgICAgICBoZWlnaHQ6IGNhbGMoMTAwJSAtIDUwcHgpOw0KDQogICAgICAgICAgLmFnZS1waWUtY2hhcnQgew0KICAgICAgICAgICAgaGVpZ2h0OiAxMDAlOw0KICAgICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCg0KICAgICAgICAgICAgLmFnZS1jaGFydC1wbGFjZWhvbGRlciB7DQogICAgICAgICAgICAgIGNvbG9yOiAjRkZGRkZGOw0KICAgICAgICAgICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgLy8g5a2m5Y6GDQogICAgICAuZWR1Y2F0aW9uLXNlY3Rpb24gew0KICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCByZ2JhKDAsIDUwLCAxMDAsIDAuOCkgMCUsIHJnYmEoMCwgMzAsIDYwLCAwLjkpIDEwMCUpOw0KICAgICAgICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDAsIDE4MSwgMjU0LCAwLjMpOw0KICAgICAgICBib3JkZXItcmFkaXVzOiA4cHg7DQogICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgICAgICAgcGFkZGluZzogMjBweDsNCiAgICAgICAgaGVpZ2h0OiAyNTBweDsNCg0KICAgICAgICAuZWR1Y2F0aW9uLWNvbnRlbnQgew0KICAgICAgICAgIG1hcmdpbi10b3A6IDMwcHg7DQogICAgICAgICAgaGVpZ2h0OiBjYWxjKDEwMCUgLSA1MHB4KTsNCg0KICAgICAgICAgIC5lZHVjYXRpb24tYmFycyB7DQogICAgICAgICAgICBoZWlnaHQ6IDEwMCU7DQogICAgICAgICAgICBvdmVyZmxvdy15OiBhdXRvOw0KDQogICAgICAgICAgICAuZWR1Y2F0aW9uLWl0ZW0gew0KICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KICAgICAgICAgICAgICBnYXA6IDEwcHg7DQoNCiAgICAgICAgICAgICAgLmVkdWNhdGlvbi1sYWJlbCB7DQogICAgICAgICAgICAgICAgd2lkdGg6IDYwcHg7DQogICAgICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICAgICAgICAgIGNvbG9yOiAjRkZGRkZGOw0KICAgICAgICAgICAgICAgIGZsZXgtc2hyaW5rOiAwOw0KICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgLmVkdWNhdGlvbi1iYXIgew0KICAgICAgICAgICAgICAgIGZsZXg6IDE7DQogICAgICAgICAgICAgICAgaGVpZ2h0OiA4cHg7DQogICAgICAgICAgICAgICAgYmFja2dyb3VuZDogcmdiYSgwLCAxODEsIDI1NCwgMC4yKTsNCiAgICAgICAgICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgICAgICAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsNCg0KICAgICAgICAgICAgICAgIC5lZHVjYXRpb24tcHJvZ3Jlc3Mgew0KICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAxMDAlOw0KICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCAjMDBENEZGIDAlLCAjMDA4MEZGIDEwMCUpOw0KICAgICAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbjogd2lkdGggMC4zcyBlYXNlOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIC5lZHVjYXRpb24tdmFsdWUgew0KICAgICAgICAgICAgICAgIHdpZHRoOiAzMHB4Ow0KICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgICAgICAgICAgICBjb2xvcjogIzAwRDRGRjsNCiAgICAgICAgICAgICAgICB0ZXh0LWFsaWduOiByaWdodDsNCiAgICAgICAgICAgICAgICBmbGV4LXNocmluazogMDsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAvLyDlhZrmtL7liIbluIMNCiAgICAgIC5wYXJ0eS1kaXN0cmlidXRpb24tc2VjdGlvbiB7DQogICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHJnYmEoMCwgNTAsIDEwMCwgMC44KSAwJSwgcmdiYSgwLCAzMCwgNjAsIDAuOSkgMTAwJSk7DQogICAgICAgIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMCwgMTgxLCAyNTQsIDAuMyk7DQogICAgICAgIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlOw0KICAgICAgICBwYWRkaW5nOiAyMHB4Ow0KICAgICAgICBoZWlnaHQ6IDMwMHB4Ow0KICAgICAgICBncmlkLWNvbHVtbjogMSAvIC0xOyAvLyDot6jotorkuKTliJcNCg0KICAgICAgICAucGFydHktY29udGVudCB7DQogICAgICAgICAgbWFyZ2luLXRvcDogMzBweDsNCiAgICAgICAgICBoZWlnaHQ6IGNhbGMoMTAwJSAtIDUwcHgpOw0KDQogICAgICAgICAgLnBhcnR5LWNoYXJ0LWNvbnRhaW5lciB7DQogICAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgICAgaGVpZ2h0OiAxMDAlOw0KICAgICAgICAgICAgZ2FwOiAyMHB4Ow0KDQogICAgICAgICAgICAucGFydHktcGllLWNoYXJ0IHsNCiAgICAgICAgICAgICAgd2lkdGg6IDIwMHB4Ow0KICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCg0KICAgICAgICAgICAgICAucGFydHktY2hhcnQtcGxhY2Vob2xkZXIgew0KICAgICAgICAgICAgICAgIGNvbG9yOiAjRkZGRkZGOw0KICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICAgICAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgLnBhcnR5LWxlZ2VuZCB7DQogICAgICAgICAgICAgIGZsZXg6IDE7DQogICAgICAgICAgICAgIGRpc3BsYXk6IGdyaWQ7DQogICAgICAgICAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyIDFmcjsNCiAgICAgICAgICAgICAgZ2FwOiA4cHg7DQogICAgICAgICAgICAgIG1heC1oZWlnaHQ6IDEwMCU7DQogICAgICAgICAgICAgIG92ZXJmbG93LXk6IGF1dG87DQoNCiAgICAgICAgICAgICAgLnBhcnR5LWxlZ2VuZC1pdGVtIHsNCiAgICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgICAgICAgICAgZ2FwOiA4cHg7DQogICAgICAgICAgICAgICAgZm9udC1zaXplOiAxMnB4Ow0KDQogICAgICAgICAgICAgICAgLmxlZ2VuZC1jb2xvciB7DQogICAgICAgICAgICAgICAgICB3aWR0aDogMTJweDsNCiAgICAgICAgICAgICAgICAgIGhlaWdodDogMTJweDsNCiAgICAgICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDJweDsNCiAgICAgICAgICAgICAgICAgIGZsZXgtc2hyaW5rOiAwOw0KICAgICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAgIC5sZWdlbmQtbGFiZWwgew0KICAgICAgICAgICAgICAgICAgY29sb3I6ICNGRkZGRkY7DQogICAgICAgICAgICAgICAgICBmbGV4OiAxOw0KICAgICAgICAgICAgICAgICAgbWluLXdpZHRoOiAwOw0KICAgICAgICAgICAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgICAgICAgICAgICAgICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOw0KICAgICAgICAgICAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDsNCiAgICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgICAubGVnZW5kLXBlcmNlbnRhZ2Ugew0KICAgICAgICAgICAgICAgICAgY29sb3I6ICMwMEQ0RkY7DQogICAgICAgICAgICAgICAgICBmb250LXdlaWdodDogNTAwOw0KICAgICAgICAgICAgICAgICAgd2lkdGg6IDMwcHg7DQogICAgICAgICAgICAgICAgICB0ZXh0LWFsaWduOiByaWdodDsNCiAgICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgICAubGVnZW5kLXZhbHVlIHsNCiAgICAgICAgICAgICAgICAgIGNvbG9yOiAjRkZENjAwOw0KICAgICAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICAgICAgICAgICAgICAgIHdpZHRoOiAyNXB4Ow0KICAgICAgICAgICAgICAgICAgdGV4dC1hbGlnbjogcmlnaHQ7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIC8vIOiuqOiuuuS6uuWRmOe7n+iuoQ0KICAgICAgLmRpc2N1c3Npb24tc3RhdHMtc2VjdGlvbiB7DQogICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHJnYmEoMCwgNTAsIDEwMCwgMC44KSAwJSwgcmdiYSgwLCAzMCwgNjAsIDAuOSkgMTAwJSk7DQogICAgICAgIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMCwgMTgxLCAyNTQsIDAuMyk7DQogICAgICAgIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlOw0KICAgICAgICBwYWRkaW5nOiAyMHB4Ow0KICAgICAgICBoZWlnaHQ6IDMwMHB4Ow0KICAgICAgICBncmlkLWNvbHVtbjogMSAvIC0xOyAvLyDot6jotorkuKTliJcNCg0KICAgICAgICAuZGlzY3Vzc2lvbi1jb250ZW50IHsNCiAgICAgICAgICBtYXJnaW4tdG9wOiAzMHB4Ow0KICAgICAgICAgIGhlaWdodDogY2FsYygxMDAlIC0gNTBweCk7DQogICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KDQogICAgICAgICAgLmRpc2N1c3Npb24tY2hhcnQtcGxhY2Vob2xkZXIgew0KICAgICAgICAgICAgY29sb3I6ICNGRkZGRkY7DQogICAgICAgICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KDQogICAgLnJpZ2h0LXBhbmVsIHsNCg0KICAgICAgLy8g55WM5Yir5YiG5p6QDQogICAgICAuc2VjdG9yLWFuYWx5c2lzLXNlY3Rpb24gew0KICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCByZ2JhKDAsIDUwLCAxMDAsIDAuOCkgMCUsIHJnYmEoMCwgMzAsIDYwLCAwLjkpIDEwMCUpOw0KICAgICAgICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDAsIDE4MSwgMjU0LCAwLjMpOw0KICAgICAgICBib3JkZXItcmFkaXVzOiA4cHg7DQogICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgICAgICAgcGFkZGluZzogMjBweDsNCiAgICAgICAgaGVpZ2h0OiAxMDAlOw0KDQogICAgICAgIC5zZWN0b3ItY29udGVudCB7DQogICAgICAgICAgbWFyZ2luLXRvcDogMzBweDsNCiAgICAgICAgICBoZWlnaHQ6IGNhbGMoMTAwJSAtIDUwcHgpOw0KDQogICAgICAgICAgLnNlY3Rvci1iYXJzIHsNCiAgICAgICAgICAgIGhlaWdodDogMTAwJTsNCiAgICAgICAgICAgIG92ZXJmbG93LXk6IGF1dG87DQogICAgICAgICAgICBwYWRkaW5nLXJpZ2h0OiAxMHB4Ow0KDQogICAgICAgICAgICAmOjotd2Via2l0LXNjcm9sbGJhciB7DQogICAgICAgICAgICAgIHdpZHRoOiA2cHg7DQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICY6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHsNCiAgICAgICAgICAgICAgYmFja2dyb3VuZDogcmdiYSgwLCAzMCwgNjAsIDAuMyk7DQogICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDNweDsNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgJjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIgew0KICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDIxMiwgMjU1LCAwLjQpOw0KICAgICAgICAgICAgICBib3JkZXItcmFkaXVzOiAzcHg7DQoNCiAgICAgICAgICAgICAgJjpob3ZlciB7DQogICAgICAgICAgICAgICAgYmFja2dyb3VuZDogcmdiYSgwLCAyMTIsIDI1NSwgMC42KTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAuc2VjdG9yLWl0ZW0gew0KICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxMnB4Ow0KICAgICAgICAgICAgICBnYXA6IDEwcHg7DQogICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsNCg0KICAgICAgICAgICAgICAuc2VjdG9yLWxhYmVsIHsNCiAgICAgICAgICAgICAgICB3aWR0aDogMTIwcHg7DQogICAgICAgICAgICAgICAgY29sb3I6ICNGRkZGRkY7DQogICAgICAgICAgICAgICAgZmxleC1zaHJpbms6IDA7DQogICAgICAgICAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgICAgICAgICAgICAgICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsNCiAgICAgICAgICAgICAgICB3aGl0ZS1zcGFjZTogbm93cmFwOw0KICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgLnNlY3Rvci1iYXIgew0KICAgICAgICAgICAgICAgIGZsZXg6IDE7DQogICAgICAgICAgICAgICAgaGVpZ2h0OiA2cHg7DQogICAgICAgICAgICAgICAgYmFja2dyb3VuZDogcmdiYSgwLCAxODEsIDI1NCwgMC4yKTsNCiAgICAgICAgICAgICAgICBib3JkZXItcmFkaXVzOiAzcHg7DQogICAgICAgICAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsNCg0KICAgICAgICAgICAgICAgIC5zZWN0b3ItcHJvZ3Jlc3Mgew0KICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAxMDAlOw0KICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCAjMDBENEZGIDAlLCAjMDA4MEZGIDEwMCUpOw0KICAgICAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogM3B4Ow0KICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbjogd2lkdGggMC4zcyBlYXNlOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIC5zZWN0b3ItdmFsdWUgew0KICAgICAgICAgICAgICAgIHdpZHRoOiAyNXB4Ow0KICAgICAgICAgICAgICAgIGNvbG9yOiAjMDBENEZGOw0KICAgICAgICAgICAgICAgIHRleHQtYWxpZ246IHJpZ2h0Ow0KICAgICAgICAgICAgICAgIGZsZXgtc2hyaW5rOiAwOw0KICAgICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["committeeStatisticsBox.vue"], "names": [], "mappings": ";AAo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file": "committeeStatisticsBox.vue", "sourceRoot": "src/views/smartBrainLargeScreen/committeeStatistics", "sourcesContent": ["<template>\r\n  <div class=\"big-screen\" ref=\"bigScreen\">\r\n    <div class=\"screen-header\">\r\n      <div class=\"header-left\">\r\n        <span class=\"date-time\">{{ currentTime }}</span>\r\n        <span class=\"weather\">晴 24℃ 东南风</span>\r\n      </div>\r\n      <div class=\"header-center\">\r\n        <img src=\"../../../assets/largeScreen/top_header_txt.png\" alt=\"\" style=\"height: 50px;\">\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <div class=\"header-buttons\">\r\n          <div class=\"header-btn area-select-btn\">\r\n            <span>选择地区</span>\r\n          </div>\r\n          <div class=\"header-btn current-module-btn\">\r\n            <span>委员统计</span>\r\n          </div>\r\n          <div class=\"header-btn home-btn\" @click=\"goHome\">\r\n            <span>返回首页</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"screen-content\">\r\n      <div class=\"left-panel\">\r\n        <!-- 委员数量 -->\r\n        <div class=\"committee-count-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">委员数量</span>\r\n          </div>\r\n          <div class=\"count-content\">\r\n            <div class=\"count-item\">\r\n              <div class=\"count-value\">418</div>\r\n              <div class=\"count-label\">委员总数</div>\r\n            </div>\r\n            <div class=\"count-item\">\r\n              <div class=\"count-value\">42</div>\r\n              <div class=\"count-label\">政协常委</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 性别比例 -->\r\n        <div class=\"gender-ratio-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">性别比例</span>\r\n          </div>\r\n          <div class=\"gender-content\">\r\n            <div class=\"gender-chart\">\r\n              <div class=\"ratio-item\">\r\n                <div class=\"ratio-circle male\">70%</div>\r\n              </div>\r\n              <div class=\"ratio-item\">\r\n                <div class=\"ratio-circle female\">30%</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 年龄 -->\r\n        <div class=\"age-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">年龄</span>\r\n          </div>\r\n          <div class=\"age-content\">\r\n            <div class=\"age-pie-chart\">\r\n              <!-- 年龄饼图占位 -->\r\n              <div class=\"age-chart-placeholder\">年龄分布图</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 学历 -->\r\n        <div class=\"education-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">学历</span>\r\n          </div>\r\n          <div class=\"education-content\">\r\n            <div class=\"education-bars\">\r\n              <div v-for=\"(item, index) in educationData\" :key=\"index\" class=\"education-item\">\r\n                <div class=\"education-label\">{{ item.name }}</div>\r\n                <div class=\"education-bar\">\r\n                  <div class=\"education-progress\" :style=\"`width: ${item.percentage}%`\"></div>\r\n                </div>\r\n                <div class=\"education-value\">{{ item.value }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 党派分布 -->\r\n        <div class=\"party-distribution-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">党派分布</span>\r\n          </div>\r\n          <div class=\"party-content\">\r\n            <div class=\"party-chart-container\">\r\n              <div class=\"party-pie-chart\">\r\n                <!-- 党派分布饼图占位 -->\r\n                <div class=\"party-chart-placeholder\">党派分布图</div>\r\n              </div>\r\n              <div class=\"party-legend\">\r\n                <div v-for=\"(item, index) in partyData\" :key=\"index\" class=\"party-legend-item\">\r\n                  <div class=\"legend-color\" :style=\"`background-color: ${item.color}`\"></div>\r\n                  <span class=\"legend-label\">{{ item.name }}</span>\r\n                  <span class=\"legend-percentage\">{{ item.percentage }}%</span>\r\n                  <span class=\"legend-value\">{{ item.value }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 讨论人员统计 -->\r\n        <div class=\"discussion-stats-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">讨论人员统计</span>\r\n          </div>\r\n          <div class=\"discussion-content\">\r\n            <div class=\"discussion-chart-placeholder\">讨论人员统计图</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"right-panel\">\r\n        <!-- 界别分析 -->\r\n        <div class=\"sector-analysis-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">界别分析</span>\r\n          </div>\r\n          <div class=\"sector-content\">\r\n            <div class=\"sector-bars\">\r\n              <div v-for=\"(item, index) in sectorData\" :key=\"index\" class=\"sector-item\">\r\n                <div class=\"sector-label\">{{ item.name }}</div>\r\n                <div class=\"sector-bar\">\r\n                  <div class=\"sector-progress\" :style=\"`width: ${item.percentage}%`\"></div>\r\n                </div>\r\n                <div class=\"sector-value\">{{ item.value }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { useIndex } from '../screen.js'\r\n\r\nexport default {\r\n  name: 'BigScreen',\r\n  components: {\r\n  },\r\n  data () {\r\n    return {\r\n      currentTime: '',\r\n      // 学历数据\r\n      educationData: [\r\n        { name: '博士', value: 101, percentage: 90 },\r\n        { name: '硕士', value: 89, percentage: 75 },\r\n        { name: '本科', value: 62, percentage: 60 },\r\n        { name: '大专', value: 45, percentage: 45 },\r\n        { name: '其他', value: 165, percentage: 100 },\r\n        { name: '研究生', value: 84, percentage: 70 }\r\n      ],\r\n      // 党派数据\r\n      partyData: [\r\n        { name: '中国共产党', value: 32, percentage: 15, color: '#FF6B6B' },\r\n        { name: '民革', value: 15, percentage: 8, color: '#4ECDC4' },\r\n        { name: '民盟', value: 14, percentage: 7, color: '#45B7D1' },\r\n        { name: '民建', value: 13, percentage: 6, color: '#96CEB4' },\r\n        { name: '民进', value: 12, percentage: 5, color: '#FFEAA7' },\r\n        { name: '农工党', value: 10, percentage: 4, color: '#DDA0DD' },\r\n        { name: '致公党', value: 8, percentage: 3, color: '#98D8C8' },\r\n        { name: '九三学社', value: 7, percentage: 3, color: '#F7DC6F' },\r\n        { name: '台盟', value: 6, percentage: 2, color: '#BB8FCE' },\r\n        { name: '无党派人士', value: 5, percentage: 2, color: '#85C1E9' }\r\n      ],\r\n      // 界别分析数据\r\n      sectorData: [\r\n        { name: '经济界', value: 32, percentage: 90 },\r\n        { name: '教育界', value: 15, percentage: 45 },\r\n        { name: '科技界', value: 14, percentage: 40 },\r\n        { name: '工商界', value: 13, percentage: 38 },\r\n        { name: '医药卫生界', value: 12, percentage: 35 },\r\n        { name: '社会科学界', value: 10, percentage: 30 },\r\n        { name: '工会', value: 8, percentage: 25 },\r\n        { name: '共青团', value: 7, percentage: 20 },\r\n        { name: '妇联', value: 6, percentage: 18 },\r\n        { name: '科协', value: 5, percentage: 15 },\r\n        { name: '台联', value: 7, percentage: 20 },\r\n        { name: '侨联', value: 3, percentage: 10 },\r\n        { name: '文化艺术界', value: 24, percentage: 70 },\r\n        { name: '体育界', value: 16, percentage: 48 },\r\n        { name: '少数民族界', value: 20, percentage: 60 },\r\n        { name: '宗教界', value: 27, percentage: 75 },\r\n        { name: '特邀人士', value: 21, percentage: 65 },\r\n        { name: '港澳台侨', value: 5, percentage: 15 },\r\n        { name: '对外友好界', value: 19, percentage: 55 },\r\n        { name: '社会福利和社会保障界', value: 12, percentage: 35 },\r\n        { name: '社会治理和社会组织界', value: 21, percentage: 65 }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n  },\r\n  mounted () {\r\n    this.initScreen()\r\n    this.updateTime()\r\n    this.timeInterval = setInterval(this.updateTime, 1000)\r\n  },\r\n  beforeDestroy () {\r\n    if (this.timeInterval) {\r\n      clearInterval(this.timeInterval)\r\n    }\r\n  },\r\n  methods: {\r\n    initScreen () {\r\n      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)\r\n      calcRate()\r\n      windowDraw()\r\n    },\r\n    updateTime () {\r\n      const now = new Date()\r\n      this.currentTime = now.toLocaleString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit',\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        second: '2-digit'\r\n      })\r\n    },\r\n    // 返回首页\r\n    goHome () {\r\n      this.$router.push({ path: '/homeBox' })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.big-screen {\r\n  width: 1920px;\r\n  height: 1080px;\r\n  position: relative;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  transform-origin: left top;\r\n  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;\r\n  background-size: cover;\r\n  background-position: center;\r\n\r\n  .screen-header {\r\n    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;\r\n    background-size: 100% 100%;\r\n    background-position: center;\r\n    height: 65px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 40px;\r\n\r\n    .header-left {\r\n      display: flex;\r\n      gap: 20px;\r\n      font-size: 14px;\r\n      color: #8cc8ff;\r\n      flex: 1;\r\n    }\r\n\r\n    .header-center {\r\n      width: 60%;\r\n      text-align: center;\r\n    }\r\n\r\n    .header-right {\r\n      flex: 1;\r\n      display: flex;\r\n      justify-content: flex-end;\r\n      align-items: center;\r\n\r\n      .header-buttons {\r\n        display: flex;\r\n        gap: 15px;\r\n\r\n        .header-btn {\r\n          padding: 8px 20px;\r\n          border-radius: 4px;\r\n          cursor: pointer;\r\n          font-size: 14px;\r\n          color: #FFFFFF;\r\n          background: linear-gradient(135deg, rgba(0, 181, 254, 0.8) 0%, rgba(0, 100, 200, 0.8) 100%);\r\n          border: 1px solid rgba(0, 181, 254, 0.6);\r\n          transition: all 0.3s ease;\r\n          position: relative;\r\n          overflow: hidden;\r\n\r\n          &::before {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0;\r\n            left: -100%;\r\n            width: 100%;\r\n            height: 100%;\r\n            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n            transition: left 0.5s;\r\n          }\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 1) 0%, rgba(0, 120, 220, 1) 100%);\r\n            border-color: rgba(0, 181, 254, 1);\r\n            transform: translateY(-2px);\r\n            box-shadow: 0 4px 12px rgba(0, 181, 254, 0.4);\r\n\r\n            &::before {\r\n              left: 100%;\r\n            }\r\n          }\r\n\r\n          &.current-module-btn {\r\n            background: linear-gradient(135deg, rgba(255, 214, 0, 0.9) 0%, rgba(255, 165, 0, 0.9) 100%);\r\n            border-color: rgba(255, 214, 0, 0.8);\r\n            color: #333;\r\n\r\n            &:hover {\r\n              background: linear-gradient(135deg, rgba(255, 214, 0, 1) 0%, rgba(255, 165, 0, 1) 100%);\r\n              border-color: rgba(255, 214, 0, 1);\r\n            }\r\n          }\r\n\r\n          span {\r\n            position: relative;\r\n            z-index: 1;\r\n            font-weight: 500;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .screen-content {\r\n    height: calc(100% - 65px);\r\n    display: flex;\r\n    padding: 20px;\r\n    gap: 20px;\r\n\r\n    .header_box {\r\n      position: absolute;\r\n      top: 15px;\r\n      left: 24px;\r\n      right: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n\r\n      .header_text_left {\r\n        font-weight: bold;\r\n        font-size: 20px;\r\n        color: #FFFFFF;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .header_text_right {\r\n        font-size: 15px;\r\n        color: #FFD600;\r\n      }\r\n\r\n      .header_text_center {\r\n        font-size: 15px;\r\n        color: #FFFFFF;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          font-weight: 500;\r\n          font-size: 24px;\r\n          color: #02FBFB;\r\n          margin: 0 10px 0 6px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .left-panel {\r\n      width: 60%;\r\n      display: grid;\r\n      grid-template-columns: 1fr 1fr;\r\n      grid-template-rows: auto auto auto;\r\n      gap: 20px;\r\n      height: 100%;\r\n    }\r\n\r\n    .right-panel {\r\n      width: 40%;\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 20px;\r\n    }\r\n\r\n    // 左侧面板样式\r\n    .left-panel {\r\n\r\n      // 委员数量\r\n      .committee-count-section {\r\n        background: linear-gradient(135deg, rgba(0, 50, 100, 0.8) 0%, rgba(0, 30, 60, 0.9) 100%);\r\n        border: 1px solid rgba(0, 181, 254, 0.3);\r\n        border-radius: 8px;\r\n        position: relative;\r\n        padding: 20px;\r\n        height: 200px;\r\n\r\n        .count-content {\r\n          display: flex;\r\n          justify-content: space-around;\r\n          align-items: center;\r\n          height: 100%;\r\n          margin-top: 30px;\r\n\r\n          .count-item {\r\n            text-align: center;\r\n\r\n            .count-value {\r\n              font-size: 48px;\r\n              font-weight: bold;\r\n              color: #00D4FF;\r\n              margin-bottom: 10px;\r\n              text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);\r\n            }\r\n\r\n            .count-label {\r\n              font-size: 16px;\r\n              color: #FFFFFF;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 性别比例\r\n      .gender-ratio-section {\r\n        background: linear-gradient(135deg, rgba(0, 50, 100, 0.8) 0%, rgba(0, 30, 60, 0.9) 100%);\r\n        border: 1px solid rgba(0, 181, 254, 0.3);\r\n        border-radius: 8px;\r\n        position: relative;\r\n        padding: 20px;\r\n        height: 200px;\r\n\r\n        .gender-content {\r\n          margin-top: 30px;\r\n          height: 100%;\r\n\r\n          .gender-chart {\r\n            display: flex;\r\n            justify-content: space-around;\r\n            align-items: center;\r\n            height: 100%;\r\n\r\n            .ratio-item {\r\n              text-align: center;\r\n\r\n              .ratio-circle {\r\n                width: 80px;\r\n                height: 80px;\r\n                border-radius: 50%;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                font-size: 18px;\r\n                font-weight: bold;\r\n                color: #FFFFFF;\r\n                margin-bottom: 10px;\r\n\r\n                &.male {\r\n                  background: conic-gradient(#00D4FF 0% 70%, rgba(0, 212, 255, 0.2) 70% 100%);\r\n                }\r\n\r\n                &.female {\r\n                  background: conic-gradient(#FFD600 0% 30%, rgba(255, 214, 0, 0.2) 30% 100%);\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 年龄\r\n      .age-section {\r\n        background: linear-gradient(135deg, rgba(0, 50, 100, 0.8) 0%, rgba(0, 30, 60, 0.9) 100%);\r\n        border: 1px solid rgba(0, 181, 254, 0.3);\r\n        border-radius: 8px;\r\n        position: relative;\r\n        padding: 20px;\r\n        height: 250px;\r\n\r\n        .age-content {\r\n          margin-top: 30px;\r\n          height: calc(100% - 50px);\r\n\r\n          .age-pie-chart {\r\n            height: 100%;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n\r\n            .age-chart-placeholder {\r\n              color: #FFFFFF;\r\n              font-size: 16px;\r\n              text-align: center;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 学历\r\n      .education-section {\r\n        background: linear-gradient(135deg, rgba(0, 50, 100, 0.8) 0%, rgba(0, 30, 60, 0.9) 100%);\r\n        border: 1px solid rgba(0, 181, 254, 0.3);\r\n        border-radius: 8px;\r\n        position: relative;\r\n        padding: 20px;\r\n        height: 250px;\r\n\r\n        .education-content {\r\n          margin-top: 30px;\r\n          height: calc(100% - 50px);\r\n\r\n          .education-bars {\r\n            height: 100%;\r\n            overflow-y: auto;\r\n\r\n            .education-item {\r\n              display: flex;\r\n              align-items: center;\r\n              margin-bottom: 15px;\r\n              gap: 10px;\r\n\r\n              .education-label {\r\n                width: 60px;\r\n                font-size: 14px;\r\n                color: #FFFFFF;\r\n                flex-shrink: 0;\r\n              }\r\n\r\n              .education-bar {\r\n                flex: 1;\r\n                height: 8px;\r\n                background: rgba(0, 181, 254, 0.2);\r\n                border-radius: 4px;\r\n                overflow: hidden;\r\n\r\n                .education-progress {\r\n                  height: 100%;\r\n                  background: linear-gradient(90deg, #00D4FF 0%, #0080FF 100%);\r\n                  border-radius: 4px;\r\n                  transition: width 0.3s ease;\r\n                }\r\n              }\r\n\r\n              .education-value {\r\n                width: 30px;\r\n                font-size: 14px;\r\n                color: #00D4FF;\r\n                text-align: right;\r\n                flex-shrink: 0;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 党派分布\r\n      .party-distribution-section {\r\n        background: linear-gradient(135deg, rgba(0, 50, 100, 0.8) 0%, rgba(0, 30, 60, 0.9) 100%);\r\n        border: 1px solid rgba(0, 181, 254, 0.3);\r\n        border-radius: 8px;\r\n        position: relative;\r\n        padding: 20px;\r\n        height: 300px;\r\n        grid-column: 1 / -1; // 跨越两列\r\n\r\n        .party-content {\r\n          margin-top: 30px;\r\n          height: calc(100% - 50px);\r\n\r\n          .party-chart-container {\r\n            display: flex;\r\n            height: 100%;\r\n            gap: 20px;\r\n\r\n            .party-pie-chart {\r\n              width: 200px;\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: center;\r\n\r\n              .party-chart-placeholder {\r\n                color: #FFFFFF;\r\n                font-size: 16px;\r\n                text-align: center;\r\n              }\r\n            }\r\n\r\n            .party-legend {\r\n              flex: 1;\r\n              display: grid;\r\n              grid-template-columns: 1fr 1fr;\r\n              gap: 8px;\r\n              max-height: 100%;\r\n              overflow-y: auto;\r\n\r\n              .party-legend-item {\r\n                display: flex;\r\n                align-items: center;\r\n                gap: 8px;\r\n                font-size: 12px;\r\n\r\n                .legend-color {\r\n                  width: 12px;\r\n                  height: 12px;\r\n                  border-radius: 2px;\r\n                  flex-shrink: 0;\r\n                }\r\n\r\n                .legend-label {\r\n                  color: #FFFFFF;\r\n                  flex: 1;\r\n                  min-width: 0;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  white-space: nowrap;\r\n                }\r\n\r\n                .legend-percentage {\r\n                  color: #00D4FF;\r\n                  font-weight: 500;\r\n                  width: 30px;\r\n                  text-align: right;\r\n                }\r\n\r\n                .legend-value {\r\n                  color: #FFD600;\r\n                  font-weight: 500;\r\n                  width: 25px;\r\n                  text-align: right;\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 讨论人员统计\r\n      .discussion-stats-section {\r\n        background: linear-gradient(135deg, rgba(0, 50, 100, 0.8) 0%, rgba(0, 30, 60, 0.9) 100%);\r\n        border: 1px solid rgba(0, 181, 254, 0.3);\r\n        border-radius: 8px;\r\n        position: relative;\r\n        padding: 20px;\r\n        height: 300px;\r\n        grid-column: 1 / -1; // 跨越两列\r\n\r\n        .discussion-content {\r\n          margin-top: 30px;\r\n          height: calc(100% - 50px);\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n\r\n          .discussion-chart-placeholder {\r\n            color: #FFFFFF;\r\n            font-size: 16px;\r\n            text-align: center;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .right-panel {\r\n\r\n      // 界别分析\r\n      .sector-analysis-section {\r\n        background: linear-gradient(135deg, rgba(0, 50, 100, 0.8) 0%, rgba(0, 30, 60, 0.9) 100%);\r\n        border: 1px solid rgba(0, 181, 254, 0.3);\r\n        border-radius: 8px;\r\n        position: relative;\r\n        padding: 20px;\r\n        height: 100%;\r\n\r\n        .sector-content {\r\n          margin-top: 30px;\r\n          height: calc(100% - 50px);\r\n\r\n          .sector-bars {\r\n            height: 100%;\r\n            overflow-y: auto;\r\n            padding-right: 10px;\r\n\r\n            &::-webkit-scrollbar {\r\n              width: 6px;\r\n            }\r\n\r\n            &::-webkit-scrollbar-track {\r\n              background: rgba(0, 30, 60, 0.3);\r\n              border-radius: 3px;\r\n            }\r\n\r\n            &::-webkit-scrollbar-thumb {\r\n              background: rgba(0, 212, 255, 0.4);\r\n              border-radius: 3px;\r\n\r\n              &:hover {\r\n                background: rgba(0, 212, 255, 0.6);\r\n              }\r\n            }\r\n\r\n            .sector-item {\r\n              display: flex;\r\n              align-items: center;\r\n              margin-bottom: 12px;\r\n              gap: 10px;\r\n              font-size: 12px;\r\n\r\n              .sector-label {\r\n                width: 120px;\r\n                color: #FFFFFF;\r\n                flex-shrink: 0;\r\n                overflow: hidden;\r\n                text-overflow: ellipsis;\r\n                white-space: nowrap;\r\n              }\r\n\r\n              .sector-bar {\r\n                flex: 1;\r\n                height: 6px;\r\n                background: rgba(0, 181, 254, 0.2);\r\n                border-radius: 3px;\r\n                overflow: hidden;\r\n\r\n                .sector-progress {\r\n                  height: 100%;\r\n                  background: linear-gradient(90deg, #00D4FF 0%, #0080FF 100%);\r\n                  border-radius: 3px;\r\n                  transition: width 0.3s ease;\r\n                }\r\n              }\r\n\r\n              .sector-value {\r\n                width: 25px;\r\n                color: #00D4FF;\r\n                text-align: right;\r\n                flex-shrink: 0;\r\n                font-weight: 500;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}