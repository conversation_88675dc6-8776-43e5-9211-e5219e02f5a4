{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\home\\homeBox.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\home\\homeBox.vue", "mtime": 1755586561902}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["homeBox.vue"], "names": [], "mappings": ";AA0MA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "homeBox.vue", "sourceRoot": "src/views/smartBrainLargeScreen/home", "sourcesContent": ["<template>\n  <div class=\"big-screen\" ref=\"bigScreen\">\n    <div class=\"screen-header\">\n      <div class=\"header-left\">\n        <span class=\"date-time\">{{ currentTime }}</span>\n        <span class=\"weather\">晴 24℃ 东南风</span>\n      </div>\n      <div class=\"header-center\">\n        <img src=\"../../../assets/largeScreen/top_header_txt.png\" alt=\"\" style=\"height: 50px;\">\n      </div>\n      <div class=\"header-right\"></div>\n    </div>\n    <div class=\"screen-content\">\n      <div class=\"left-panel\">\n        <!-- 委员统计 -->\n        <div class=\"committee_statistics\">\n          <div class=\"header_box\">\n            <span class=\"header_text_left\" @click=\"handleCommitteeClick\">委员统计</span>\n            <span class=\"header_text_right\">十二届二次</span>\n          </div>\n          <div class=\"committee_statistics_content\">\n            <div class=\"committee_statistics_num\">\n              <div v-for=\"(item, index) in committeeStatisticsNum\" :key=\"index\" class=\"num_box\">\n                <img :src=\"item.icon\" alt=\"\" class=\"num_icon\">\n                <div>\n                  <div class=\"num_label\">{{ item.label }}</div>\n                  <div class=\"num_value\" :style=\"`color:${item.color}`\">{{ item.value }}</div>\n                </div>\n              </div>\n            </div>\n            <div class=\"committee_statistics_chart\">\n              <BarScrollChart id=\"committee-statistics\" :chart-data=\"committeeBarData\" />\n            </div>\n          </div>\n        </div>\n        <!-- 提案统计 -->\n        <div class=\"proposal_statistics\">\n          <div class=\"header_box\">\n            <span class=\"header_text_left\">提案统计</span>\n            <span class=\"header_text_right\">十二届二次会议</span>\n            <span class=\"header_text_center\">提交提案总数：<span>873</span>件</span>\n          </div>\n          <div class=\"proposal_statistics_content\">\n            <div class=\"proposal_statistics_num\">\n              <div v-for=\"(item, index) in proposalStatisticsNum\" :key=\"index\" class=\"num_box\">\n                <img :src=\"item.icon\" alt=\"\" class=\"num_icon\">\n                <div>\n                  <div class=\"num_label\">{{ item.label }}</div>\n                  <div class=\"num_value\" :style=\"`color:${item.color}`\">{{ item.value }}<span class=\"num_unit\">{{\n                    item.unit }}</span></div>\n                </div>\n              </div>\n            </div>\n            <div class=\"proposal_statistics_chart\">\n              <PieChart id=\"proposal-statistics\" :chart-data=\"proposalChartData\" :name=\"proposalChartName\" />\n            </div>\n          </div>\n        </div>\n        <!-- 工作动态 -->\n        <div class=\"work_dynamics\">\n          <div class=\"header_box\">\n            <span class=\"header_text_left\">工作动态</span>\n            <span class=\"header_text_right\">本年</span>\n          </div>\n          <div class=\"work_dynamics_content\">\n            <div class=\"dynamics-list\">\n              <div v-for=\"(item, index) in workDynamicsData\" :key=\"item.id\" class=\"dynamics-item\"\n                :class=\"{ 'with-bg-image': index % 2 === 0, 'with-bg-color': index % 2 === 1 }\">\n                <div class=\"dynamics-content\">\n                  <div class=\"dynamics-title\">{{ item.title }}</div>\n                  <div class=\"dynamics-date\">{{ item.date }}</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div class=\"center-panel\">\n        <!-- 地图 -->\n        <div class=\"map_box\">\n          <MapComponent :data=\"mapData\" :areaId=\"areaId + ''\" :areaName=\"areaName\" @region-click=\"handleRegionClick\" />\n        </div>\n        <!-- 履职统计 -->\n        <div class=\"performance_statistics\">\n          <div class=\"header_box\">\n            <span class=\"header_text_left\">履职统计</span>\n            <span class=\"header_text_right\">十二届二次</span>\n          </div>\n          <div class=\"performance_statistics_content\">\n            <div class=\"table-container\">\n              <!-- 固定表头 -->\n              <div class=\"table-header\">\n                <div class=\"header-cell\">姓名</div>\n                <div class=\"header-cell\">会议活动</div>\n                <div class=\"header-cell\">政协提案</div>\n                <div class=\"header-cell\">社情民意</div>\n                <div class=\"header-cell\">议政建言</div>\n                <div class=\"header-cell\">读书心得</div>\n                <div class=\"header-cell\">委员培训</div>\n                <div class=\"header-cell\"></div> <!-- 滚动条占位 -->\n              </div>\n              <!-- 可滚动内容 -->\n              <div class=\"table-body\">\n                <div class=\"table-row\" v-for=\"(item, index) in performanceData\" :key=\"index\">\n                  <div class=\"table-cell name-col\">{{ item.name }}</div>\n                  <div class=\"table-cell meeting-col\">{{ item.meeting }}</div>\n                  <div class=\"table-cell proposal-col\">{{ item.proposal }}</div>\n                  <div class=\"table-cell opinion-col\">{{ item.opinion }}</div>\n                  <div class=\"table-cell suggestion-col\">{{ item.suggestion }}\n                  </div>\n                  <div class=\"table-cell reading-col\">{{ item.reading }}</div>\n                  <div class=\"table-cell training-col\">{{ item.training }}</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div class=\"right-panel\">\n        <!-- 社情民意 -->\n        <div class=\"social\">\n          <div class=\"header_box\">\n            <span class=\"header_text_left\">社情民意</span>\n            <span class=\"header_text_right\">本年</span>\n          </div>\n          <div class=\"social_content\">\n            <div class=\"social-data-container\">\n              <div class=\"left-data-item\">\n                <div class=\"left-data-label\">委员报送</div>\n                <div class=\"left-data-value\">总数<span>{{ socialData.memberSubmit.count }}</span>篇</div>\n                <div class=\"left-data-detail\">采用<span>{{ socialData.memberSubmit.adopted }}</span> 篇</div>\n              </div>\n              <div class=\"center-chart\">\n                <div class=\"progress-content\">\n                  <div class=\"total-number\">{{ socialData.total }}</div>\n                  <div class=\"total-label\">总数</div>\n                </div>\n              </div>\n              <div class=\"right-data-item\">\n                <div class=\"right-data-label\">单位报送</div>\n                <div class=\"right-data-value\">总数<span>{{ socialData.unitSubmit.count }}</span>篇</div>\n                <div class=\"right-data-detail\">采用<span>{{ socialData.unitSubmit.adopted }}</span> 篇</div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <!-- 会议活动 -->\n        <div class=\"conference_activities\">\n          <div class=\"header_box\">\n            <span class=\"header_text_left\">会议活动</span>\n            <span class=\"header_text_right\">本年</span>\n          </div>\n          <div class=\"conference_activities_content\">\n            <div class=\"activities-grid\">\n              <div v-for=\"(item, index) in conferenceActivitiesData\" :key=\"index\" class=\"activity-item\"\n                :class=\"getItemClass(item.name)\">\n                <div class=\"activity-value\">{{ item.value }}</div>\n                <div class=\"activity-name\">{{ item.name }}</div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <!-- 网络议政 -->\n        <div class=\"discussions\">\n          <div class=\"header_box\">\n            <span class=\"header_text_left\">网络议政</span>\n            <span class=\"header_text_right\"></span>\n          </div>\n          <div class=\"discussions_content\">\n            <!-- 统计数据区域 -->\n            <div class=\"statistics-section\">\n              <div v-for=\"(item, index) in discussionsData.statistics\" :key=\"index\" class=\"stat-item\">\n                <div class=\"stat-dot\"></div>\n                <div class=\"stat-info\">\n                  <span class=\"stat-name\">{{ item.name }}</span>\n                  <span class=\"stat-value\">{{ item.value }}</span>\n                  <span class=\"stat-unit\">{{ item.unit }}</span>\n                </div>\n              </div>\n            </div>\n\n            <!-- 最热话题区域 -->\n            <div class=\"hot-topics-section\">\n              <div class=\"hot-topics-header\">\n                <img src=\"../../../assets/largeScreen/icon_hot.png\" alt=\"热门\" class=\"hot-icon\">\n                <span class=\"hot-title\">最热话题</span>\n              </div>\n              <div class=\"topics-list\">\n                <div v-for=\"(topic, index) in discussionsData.hotTopics\" :key=\"index\" class=\"topic-item\">\n                  <div class=\"topic-dot\"></div>\n                  <span class=\"topic-text\">{{ topic }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { useIndex } from '../screen.js'\nimport MapComponent from '../components/MapComponent.vue'\nimport PieChart from '../components/PieChart.vue'\nimport BarScrollChart from '../components/BarScrollChart.vue'\n\nexport default {\n  name: 'BigScreen',\n  components: {\n    MapComponent,\n    PieChart,\n    BarScrollChart\n  },\n  data () {\n    return {\n      currentTime: '',\n      // 委员统计\n      committeeStatisticsNum: [\n        { icon: require('../../../assets/largeScreen/icon_cppcc_member.png'), label: '政协委员（人）', value: '10095', color: '#ffffff' },\n        { icon: require('../../../assets/largeScreen/icon_committee_member.png'), label: '政协常委', value: '8742', color: '#FCD603' }\n      ],\n      // 委员统计柱状图数据\n      committeeBarData: [\n        { name: '中共', value: 32 },\n        { name: '民革', value: 15 },\n        { name: '民盟', value: 14 },\n        { name: '民建', value: 13 },\n        { name: '民进', value: 12 },\n        { name: '农工', value: 10 },\n        { name: '致公', value: 8 },\n        { name: '九三', value: 7 },\n        { name: '台盟', value: 6 },\n        { name: '无党派', value: 5 }\n      ],\n      // 提案统计\n      proposalStatisticsNum: [\n        { icon: require('../../../assets/largeScreen/icon_committee_proposal.png'), label: '委员提案', value: '456', unit: '件', color: '#0DBCDB' },\n        { icon: require('../../../assets/largeScreen/icon_circles_proposal.png'), label: '界别提案', value: '354', unit: '件', color: '#0058FF' },\n        { icon: require('../../../assets/largeScreen/icon_committee_proposal.png'), label: '组织提案', value: '211', unit: '件', color: '#1AEBDD' }\n      ],\n      // 提案统计图表数据\n      proposalChartData: [\n        { name: '政府制约', value: 22.52 },\n        { name: '县区市政', value: 18.33 },\n        { name: '司法法治', value: 15.73 },\n        { name: '区市政府', value: 11.34 },\n        { name: '科技工商', value: 9.56 },\n        { name: '教育文化', value: 8.09 },\n        { name: '派出机构', value: 4.21 },\n        { name: '社会事业', value: 3.71 },\n        { name: '企事业', value: 3.65 },\n        { name: '农村卫生', value: 3.21 },\n        { name: '其他机构', value: 1.86 },\n        { name: '各群体他', value: 1.02 }\n      ],\n      proposalChartName: '提案统计',\n      // 工作动态数据\n      workDynamicsData: [\n        {\n          id: 1,\n          title: '市政协社会和法制工作办公室围绕\"居家适老化改造\"开展专题调研',\n          date: '2025-06-03'\n        },\n        {\n          id: 2,\n          title: '\"与民同行 共创共赢\"新格局下民营企业转型发展座谈会召开',\n          date: '2025-05-30'\n        },\n        {\n          id: 3,\n          title: '\"惠民生·基层行\"义诊活动温暖人心',\n          date: '2025-05-30'\n        },\n        {\n          id: 4,\n          title: '市科技局面复市政协科技界别提案',\n          date: '2025-05-30'\n        },\n        {\n          id: 5,\n          title: '市政协召开\"推进数字化转型\"专题协商会',\n          date: '2025-05-28'\n        },\n        {\n          id: 6,\n          title: '政协委员深入基层开展\"三服务\"活动',\n          date: '2025-05-25'\n        }\n      ],\n      // 履职统计数据\n      performanceData: [\n        { name: '马平安', meeting: 515, proposal: 15, opinion: 0, suggestion: 0, reading: 0, training: 0 },\n        { name: '马波', meeting: 400, proposal: 0, opinion: 0, suggestion: 12, reading: 0, training: 15 },\n        { name: '王玉民', meeting: 490, proposal: 1, opinion: 2, suggestion: 0, reading: 4, training: 25 },\n        { name: '王俊宝', meeting: 500, proposal: 0, opinion: 4, suggestion: 1, reading: 5, training: 60 },\n        { name: '李明', meeting: 320, proposal: 8, opinion: 1, suggestion: 3, reading: 2, training: 18 },\n        { name: '张华', meeting: 280, proposal: 5, opinion: 0, suggestion: 2, reading: 1, training: 12 },\n        { name: '刘强', meeting: 450, proposal: 3, opinion: 6, suggestion: 0, reading: 3, training: 35 },\n        { name: '陈静', meeting: 380, proposal: 2, opinion: 3, suggestion: 4, reading: 6, training: 28 }\n      ],\n      // 社情民意数据\n      socialData: {\n        memberSubmit: {\n          count: 345,\n          adopted: 21\n        },\n        unitSubmit: {\n          count: 547,\n          adopted: 79\n        },\n        total: 1057\n      },\n      // 会议活动数据\n      conferenceActivitiesData: [\n        { name: '会议次数', value: 201 },\n        { name: '活动次数', value: 310 },\n        { name: '会议人数', value: 2412 },\n        { name: '活动人数', value: 4015 }\n      ],\n      // 网络议政数据\n      discussionsData: {\n        statistics: [\n          { name: '发布议题', value: 72, unit: '个' },\n          { name: '累计参与人次', value: 39301, unit: '次' },\n          { name: '累计征求意见', value: 12306, unit: '条' }\n        ],\n        hotTopics: [\n          '推进黄河国家文化公园建设',\n          '持续推进黄河流域生态保护修复，助力\"先行区\"建设',\n          '全面加强新时代中小学劳动教育'\n        ]\n      },\n      mapData: [\n        { name: '市南区', value: '1200', areaId: '370202', adcode: 370202 },\n        { name: '市北区', value: '1300', areaId: '370203', adcode: 370203 },\n        { name: '黄岛区', value: '850', areaId: '370211', adcode: 370211 },\n        { name: '崂山区', value: '700', areaId: '370212', adcode: 370212 },\n        { name: '李沧区', value: '1000', areaId: '370213', adcode: 370213 },\n        { name: '城阳区', value: '1100', areaId: '370214', adcode: 370214 },\n        { name: '即墨区', value: '950', areaId: '370215', adcode: 370215 },\n        { name: '胶州市', value: '800', areaId: '370281', adcode: 370281 },\n        { name: '平度市', value: '1400', areaId: '370283', adcode: 370283 },\n        { name: '莱西市', value: '600', areaId: '370285', adcode: 370285 }\n      ],\n      areaId: JSON.parse(sessionStorage.getItem('areaId' + this.$logo())),\n      areaName: '青岛市'\n    }\n  },\n  computed: {\n  },\n  mounted () {\n    this.initScreen()\n    this.updateTime()\n    this.timeInterval = setInterval(this.updateTime, 1000)\n  },\n  beforeDestroy () {\n    if (this.timeInterval) {\n      clearInterval(this.timeInterval)\n    }\n  },\n  methods: {\n    initScreen () {\n      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)\n      calcRate()\n      windowDraw()\n    },\n    updateTime () {\n      const now = new Date()\n      this.currentTime = now.toLocaleString('zh-CN', {\n        year: 'numeric',\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit',\n        second: '2-digit'\n      })\n    },\n    getItemClass (name) {\n      if (name.includes('会议')) {\n        return 'meeting-item'\n      } else if (name.includes('活动')) {\n        return 'activity-item-bg'\n      }\n      return ''\n    },\n    handleRegionClick (region) {\n      console.log('选中地区:', region)\n      // 这里可以添加地区点击后的业务逻辑\n      // 比如显示该地区的详细数据等\n    },\n    // 打开委员统计\n    handleCommitteeClick () {\n      this.$router.push({ path: '/committeeStatistics', query: { route: '/committeeStatistics' } })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.big-screen {\n  width: 1920px;\n  height: 1080px;\n  position: relative;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  transform-origin: left top;\n  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;\n  background-size: cover;\n  background-position: center;\n\n  .screen-header {\n    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;\n    background-size: 100% 100%;\n    background-position: center;\n    height: 65px;\n    display: flex;\n    align-items: center;\n    padding: 0 40px;\n\n    .header-left {\n      display: flex;\n      gap: 20px;\n      font-size: 14px;\n      color: #8cc8ff;\n      flex: 1;\n    }\n\n    .header-center {\n      width: 60%;\n      text-align: center;\n    }\n\n    .header-right {\n      flex: 1;\n    }\n  }\n\n  .screen-content {\n    height: calc(100% - 65px);\n    display: flex;\n    padding: 35px 20px 0 20px;\n    gap: 30px;\n\n    .header_box {\n      position: absolute;\n      top: 15px;\n      left: 24px;\n      right: 0;\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n\n      .header_text_left {\n        font-weight: bold;\n        font-size: 20px;\n        color: #FFFFFF;\n        cursor: pointer;\n      }\n\n      .header_text_right {\n        font-size: 15px;\n        color: #FFD600;\n      }\n\n      .header_text_center {\n        font-size: 15px;\n        color: #FFFFFF;\n        display: flex;\n        align-items: center;\n\n        span {\n          font-weight: 500;\n          font-size: 24px;\n          color: #02FBFB;\n          margin: 0 10px 0 6px;\n        }\n      }\n    }\n\n    .left-panel,\n    .right-panel {\n      width: 470px;\n      display: flex;\n      flex-direction: column;\n      gap: 20px 30px;\n    }\n\n    .left-panel {\n      .committee_statistics {\n        background: url('../../../assets/largeScreen/committee_statistics_bg.png') no-repeat;\n        background-size: 100% 100%;\n        background-position: center;\n        position: relative;\n        height: 320px;\n        width: 100%;\n\n        .committee_statistics_content {\n          height: 100%;\n          margin-top: 72px;\n          margin-left: 20px;\n          margin-right: 20px;\n\n          .committee_statistics_num {\n            display: flex;\n            align-items: center;\n            justify-content: space-around;\n\n            .num_box {\n              display: flex;\n              align-items: center;\n\n              .num_icon {\n                width: 64px;\n                height: 64px;\n                margin-right: 14px;\n              }\n\n              .num_label {\n                font-size: 15px;\n                color: #B4C0CC;\n                margin-bottom: 14px;\n              }\n\n              .num_value {\n                font-weight: bold;\n                font-size: 26px;\n                color: #FFFFFF;\n              }\n            }\n          }\n        }\n      }\n\n      .proposal_statistics {\n        background: url('../../../assets/largeScreen/proposal_statistics_bg.png') no-repeat;\n        background-size: 100% 100%;\n        background-position: center;\n        position: relative;\n        height: 310px;\n        width: 100%;\n\n        .proposal_statistics_content {\n          height: 100%;\n          margin-top: 72px;\n          margin-left: 20px;\n          margin-right: 20px;\n\n          .proposal_statistics_num {\n            display: flex;\n            align-items: center;\n            justify-content: space-between;\n\n            .num_box {\n              display: flex;\n              align-items: center;\n\n              .num_icon {\n                width: 54px;\n                height: 54px;\n                margin-right: 10px;\n              }\n\n              .num_label {\n                font-size: 14px;\n                color: #FFFFFF;\n                margin-bottom: 5px;\n              }\n\n              .num_value {\n                font-size: 20px;\n                color: #0DBCDB;\n                font-weight: 500;\n\n                .num_unit {\n                  font-size: 14px;\n                  color: #FFFFFF;\n                  font-weight: normal;\n                  margin-left: 4px;\n                }\n              }\n            }\n          }\n\n          .proposal_statistics_chart {\n            height: 180px;\n            margin-top: 20px;\n          }\n        }\n      }\n\n      .work_dynamics {\n        background: url('../../../assets/largeScreen/work_dynamics_bg.png') no-repeat;\n        background-size: 100% 100%;\n        background-position: center;\n        position: relative;\n        height: 270px;\n        width: 100%;\n\n        .work_dynamics_content {\n          height: 100%;\n          margin-top: 65px;\n          margin-left: 14px;\n          margin-right: 14px;\n\n          .dynamics-list {\n            height: calc(100% - 70px);\n            overflow-y: auto;\n\n            &::-webkit-scrollbar {\n              width: 4px;\n            }\n\n            &::-webkit-scrollbar-track {\n              background: rgba(0, 30, 60, 0.3);\n              border-radius: 2px;\n            }\n\n            &::-webkit-scrollbar-thumb {\n              background: rgba(0, 212, 255, 0.4);\n              border-radius: 2px;\n\n              &:hover {\n                background: rgba(0, 212, 255, 0.6);\n              }\n            }\n\n            .dynamics-item {\n              margin-bottom: 12px;\n              overflow: hidden;\n              position: relative;\n\n              &:last-child {\n                margin-bottom: 0;\n              }\n\n              // 奇数项 - 背景图片样式\n              &.with-bg-image {\n                background: url('../../../assets/largeScreen/table_bg.png') no-repeat;\n                background-size: 100% 100%;\n                background-position: center;\n              }\n\n              // 偶数项 - 背景颜色样式\n              &.with-bg-color {\n                background: rgba(6, 79, 219, 0.05);\n              }\n\n              .dynamics-content {\n                padding: 12px 15px;\n                position: relative;\n                z-index: 2;\n                display: flex;\n                justify-content: space-between;\n                align-items: center;\n\n                .dynamics-title {\n                  flex: 1;\n                  color: #fff;\n                  font-size: 16px;\n                  margin-right: 16px;\n                  // 文本溢出处理\n                  display: -webkit-box;\n                  -webkit-line-clamp: 1;\n                  -webkit-box-orient: vertical;\n                  overflow: hidden;\n                  text-overflow: ellipsis;\n                }\n\n                .dynamics-date {\n                  flex-shrink: 0;\n                  font-size: 16px;\n                  color: #FFFFFF;\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n\n    .right-panel {\n      .social {\n        background: url('../../../assets/largeScreen/social_bg.png') no-repeat;\n        background-size: 100% 100%;\n        background-position: center;\n        position: relative;\n        height: 290px;\n        width: 100%;\n\n        .social_content {\n          height: 190px;\n          margin-top: 75px;\n          margin-left: 12px;\n          margin-right: 12px;\n          background: url('../../../assets/largeScreen/social_content_bg.png') no-repeat;\n          background-size: 100% 100%;\n          background-position: center;\n\n          .social-data-container {\n            display: flex;\n            align-items: center;\n            justify-content: space-between;\n            width: 100%;\n            height: 100%;\n\n            .left-data-item {\n              display: flex;\n              flex-direction: column;\n              align-items: center;\n              text-align: center;\n              flex: 1;\n              margin-right: 20px;\n\n              .left-data-label {\n                font-size: 14px;\n                color: #19ECFF;\n                margin-bottom: 20px;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n              }\n\n              .left-data-value {\n                font-size: 14px;\n                color: #FFFFFF;\n                margin-bottom: 15px;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n\n                span {\n                  font-weight: 400;\n                  font-size: 20px;\n                  color: #FFD600;\n                  margin: 0 5px;\n                }\n              }\n\n              .left-data-detail {\n                font-size: 14px;\n                color: #FFFFFF;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n\n                span {\n                  font-weight: 400;\n                  font-size: 20px;\n                  color: #19ECFF;\n                  margin: 0 5px;\n                }\n              }\n            }\n\n            .center-chart {\n              flex: 1;\n              display: flex;\n              justify-content: center;\n              align-items: center;\n\n              .progress-content {\n                position: absolute;\n                display: flex;\n                flex-direction: column;\n                align-items: center;\n                justify-content: center;\n\n                .total-number {\n                  font-weight: 500;\n                  font-size: 24px;\n                  color: #FFD600;\n                  margin-bottom: 8px;\n                }\n\n                .total-label {\n                  font-size: 14px;\n                  color: #ffffff;\n                }\n              }\n            }\n\n            .right-data-item {\n              display: flex;\n              flex-direction: column;\n              align-items: center;\n              text-align: center;\n              flex: 1;\n              margin-left: 20px;\n\n              .right-data-label {\n                font-size: 14px;\n                color: #19ECFF;\n                margin-bottom: 20px;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n              }\n\n              .right-data-value {\n                font-size: 14px;\n                color: #FFFFFF;\n                margin-bottom: 15px;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n\n                span {\n                  font-weight: 400;\n                  font-size: 20px;\n                  color: #FFD600;\n                  margin: 0 5px;\n                }\n              }\n\n              .right-data-detail {\n                font-size: 14px;\n                color: #FFFFFF;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n\n                span {\n                  font-weight: 400;\n                  font-size: 20px;\n                  color: #19ECFF;\n                  margin: 0 5px;\n                }\n              }\n            }\n          }\n        }\n      }\n\n      .conference_activities {\n        background: url('../../../assets/largeScreen/conference_activities_bg.png') no-repeat;\n        background-size: 100% 100%;\n        background-position: center;\n        position: relative;\n        height: 290px;\n        width: 100%;\n\n        .conference_activities_content {\n          margin-top: 70px;\n          margin-left: 20px;\n          margin-right: 20px;\n\n          .activities-grid {\n            display: grid;\n            grid-template-columns: 1fr 1fr;\n            grid-template-rows: 1fr 1fr;\n            gap: 15px;\n            height: 100%;\n\n            .activity-item {\n              display: flex;\n              flex-direction: column;\n              align-items: center;\n              justify-content: center;\n              background-size: 100% 100%;\n              background-repeat: no-repeat;\n              background-position: center;\n              height: 92px;\n\n              .activity-value {\n                font-weight: 500;\n                font-size: 32px;\n                color: #FFFFFF;\n                line-height: 24px;\n                margin-bottom: 12px;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n              }\n\n              .activity-name {\n                font-size: 16px;\n                color: #FFFFFF;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n              }\n\n              &.meeting-item {\n                background-image: url('../../../assets/largeScreen/icon_meeting_item_bg.png');\n              }\n\n              &.activity-item-bg {\n                background-image: url('../../../assets/largeScreen/icon_activity_item_bg.png');\n              }\n            }\n          }\n        }\n      }\n\n      .discussions {\n        background: url('../../../assets/largeScreen/discussions_bg.png') no-repeat;\n        background-size: 100% 100%;\n        background-position: center;\n        position: relative;\n        height: 320px;\n        width: 100%;\n\n        .discussions_content {\n          margin-top: 75px;\n          margin-left: 20px;\n          margin-right: 20px;\n          height: calc(100% - 90px);\n          display: flex;\n          flex-direction: column;\n\n          .statistics-section {\n            display: flex;\n            flex-wrap: wrap;\n            justify-content: space-between;\n            gap: 20px;\n            margin-bottom: 25px;\n\n            .stat-item {\n              display: flex;\n              align-items: center;\n              gap: 12px;\n\n              .stat-dot {\n                width: 10px;\n                height: 10px;\n                border-radius: 50%;\n                background: linear-gradient(180deg, #00EEFF 0%, #E1FDFF 100%);\n                flex-shrink: 0;\n                margin-top: 5px;\n              }\n\n              .stat-info {\n                display: flex;\n                align-items: center;\n                gap: 8px;\n\n                .stat-name {\n                  font-size: 15px;\n                  color: #FFFFFF;\n                  font-family: DIN-BoldItalic, DIN-BoldItalic;\n                }\n\n                .stat-value {\n                  font-weight: 500;\n                  font-size: 20px;\n                  color: #FFD600;\n                  font-family: DIN-BoldItalic, DIN-BoldItalic;\n                }\n\n                .stat-unit {\n                  font-size: 15px;\n                  color: #FFFFFF;\n                  font-family: DIN-BoldItalic, DIN-BoldItalic;\n                }\n              }\n            }\n          }\n\n          .hot-topics-section {\n            flex: 1;\n            background: rgba(31, 198, 255, 0.16);\n            padding: 12px 16px;\n\n            .hot-topics-header {\n              display: flex;\n              align-items: center;\n              gap: 8px;\n              margin-bottom: 15px;\n\n              .hot-icon {\n                width: 20px;\n                height: 20px;\n              }\n\n              .hot-title {\n                font-size: 16px;\n                color: #02FBFB;\n                font-weight: 500;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n              }\n            }\n\n            .topics-list {\n              display: flex;\n              flex-direction: column;\n              gap: 12px;\n\n              .topic-item {\n                display: flex;\n                align-items: flex-start;\n                gap: 10px;\n\n                .topic-dot {\n                  width: 6px;\n                  height: 6px;\n                  border-radius: 50%;\n                  background: rgba(217, 217, 217, 0.5);\n                  margin-top: 8px;\n                  flex-shrink: 0;\n                }\n\n                .topic-text {\n                  font-size: 14px;\n                  color: #FFFFFF;\n                  line-height: 22px;\n                  font-family: DIN-BoldItalic, DIN-BoldItalic;\n                  text-overflow: ellipsis;\n                  overflow: hidden;\n                  white-space: nowrap;\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n\n    .center-panel {\n      flex: 1;\n      gap: 20px;\n      display: flex;\n      flex-direction: column;\n\n      .map_box {\n        background: url('../../../assets/largeScreen/map_bg.png') no-repeat;\n        background-size: 100% 100%;\n        background-position: center;\n        height: 650px;\n        width: 100%;\n        position: relative;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      }\n\n      .performance_statistics {\n        background: url('../../../assets/largeScreen/performance_statistics_bg.png') no-repeat;\n        background-size: 100% 100%;\n        background-position: center;\n        position: relative;\n        height: 270px;\n        width: 100%;\n\n        .performance_statistics_content {\n          height: 100%;\n          margin-top: 65px;\n          margin-left: 16px;\n          margin-right: 16px;\n\n          .table-container {\n            height: calc(100% - 75px);\n            display: flex;\n            flex-direction: column;\n            border: 1px solid rgba(0, 212, 255, 0.2);\n            overflow: hidden;\n            /* 使用CSS Grid确保列对齐 */\n            --name-col-width: 120px;\n            --scrollbar-width: 6px;\n\n            .table-header {\n              display: grid;\n              grid-template-columns: var(--name-col-width) repeat(6, 1fr) var(--scrollbar-width);\n              border-bottom: 1px solid rgba(0, 212, 255, 0.4);\n              position: sticky;\n              top: 0;\n              z-index: 10;\n\n              .header-cell {\n                padding: 12px 8px;\n                text-align: center;\n                color: #E6F7FF;\n                font-size: 15px;\n                border-right: 1px solid rgba(0, 212, 255, 0.3);\n                display: flex;\n                align-items: center;\n                justify-content: center;\n\n                &:last-child {\n                  border-right: none;\n                  background: transparent;\n                  border: none;\n                }\n\n                // &.name-col {\n                //   background: rgba(0, 100, 180, 0.9);\n                //   font-weight: 600;\n                // }\n              }\n            }\n\n            .table-body {\n              flex: 1;\n              overflow-y: auto;\n\n              &::-webkit-scrollbar {\n                width: 6px;\n              }\n\n              &::-webkit-scrollbar-track {\n                background: rgba(0, 30, 60, 0.3);\n                border-radius: 3px;\n              }\n\n              &::-webkit-scrollbar-thumb {\n                background: rgba(0, 212, 255, 0.4);\n                border-radius: 3px;\n\n                &:hover {\n                  background: rgba(0, 212, 255, 0.6);\n                }\n              }\n\n              .table-row {\n                display: grid;\n                grid-template-columns: var(--name-col-width) repeat(6, 1fr);\n                border-bottom: 1px solid rgba(0, 212, 255, 0.4);\n                transition: all 0.3s ease;\n\n                &:hover {\n                  background: rgba(0, 212, 255, 0.1);\n                }\n\n                .table-cell {\n                  padding: 12px 8px;\n                  text-align: center;\n                  color: #FFFFFF;\n                  font-size: 14px;\n                  border-right: 1px solid rgba(0, 212, 255, 0.4);\n                  transition: all 0.3s ease;\n                  display: flex;\n                  align-items: center;\n                  justify-content: center;\n\n                  &:last-child {\n                    border-right: none;\n                  }\n\n                  &.name-col {\n                    background: rgba(0, 60, 120, 0.4);\n                    color: #FFF;\n                    font-weight: 500;\n                  }\n\n                  &.meeting-col {\n                    background: rgba(10, 63, 111, 0.4);\n                    color: #59F7CA;\n                    font-weight: 500;\n                    font-size: 16px;\n                  }\n\n                  &.proposal-col {\n                    background: rgba(10, 63, 111, 0.4);\n                    font-weight: 500;\n                    font-size: 16px;\n                    color: #00FFF7;\n                  }\n\n                  &.opinion-col {\n                    background: rgba(10, 63, 111, 0.4);\n                    font-weight: 500;\n                    font-size: 16px;\n                    color: #FF386B;\n                  }\n\n                  &.suggestion-col {\n                    background: rgba(10, 63, 111, 0.4);\n                    font-weight: 500;\n                    font-size: 16px;\n                    color: #81C4E4;\n                  }\n\n                  &.reading-col {\n                    background: rgba(10, 63, 111, 0.4);\n                    font-weight: 500;\n                    font-size: 16px;\n                    color: #387BFD;\n                  }\n\n                  &.training-col {\n                    background: rgba(10, 63, 111, 0.4);\n                    font-weight: 500;\n                    font-size: 16px;\n                    color: #FF911F;\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}