{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\home\\homeBox.vue?vue&type=template&id=76285fab&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\home\\homeBox.vue", "mtime": 1755575181829}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1655712169000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "ref", "staticClass", "_v", "_s", "currentTime", "_m", "_l", "committeeStatisticsNum", "item", "index", "key", "attrs", "src", "icon", "alt", "label", "style", "color", "value", "id", "committeeBarData", "proposalStatisticsNum", "unit", "proposalChartData", "name", "proposalChartName", "workDynamicsData", "class", "title", "date", "data", "mapData", "areaId", "areaName", "on", "handleRegionClick", "performanceData", "meeting", "proposal", "opinion", "suggestion", "reading", "training", "socialData", "memberSubmit", "count", "adopted", "total", "unitSubmit", "conferenceActivitiesData", "getItemClass", "discussionsData", "statistics", "hotTopics", "topic", "staticRenderFns", "staticStyle", "height", "require", "_withStripped"], "sources": ["D:/zy/xm/pc/qdzx/product/src/views/smartBrainLargeScreen/home/<USER>"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { ref: \"bigScreen\", staticClass: \"big-screen\" }, [\n    _c(\"div\", { staticClass: \"screen-header\" }, [\n      _c(\"div\", { staticClass: \"header-left\" }, [\n        _c(\"span\", { staticClass: \"date-time\" }, [\n          _vm._v(_vm._s(_vm.currentTime)),\n        ]),\n        _c(\"span\", { staticClass: \"weather\" }, [_vm._v(\"晴 24℃ 东南风\")]),\n      ]),\n      _vm._m(0),\n      _c(\"div\", { staticClass: \"header-right\" }),\n    ]),\n    _c(\"div\", { staticClass: \"screen-content\" }, [\n      _c(\"div\", { staticClass: \"left-panel\" }, [\n        _c(\"div\", { staticClass: \"committee_statistics\" }, [\n          _vm._m(1),\n          _c(\"div\", { staticClass: \"committee_statistics_content\" }, [\n            _c(\n              \"div\",\n              { staticClass: \"committee_statistics_num\" },\n              _vm._l(_vm.committeeStatisticsNum, function (item, index) {\n                return _c(\"div\", { key: index, staticClass: \"num_box\" }, [\n                  _c(\"img\", {\n                    staticClass: \"num_icon\",\n                    attrs: { src: item.icon, alt: \"\" },\n                  }),\n                  _c(\"div\", [\n                    _c(\"div\", { staticClass: \"num_label\" }, [\n                      _vm._v(_vm._s(item.label)),\n                    ]),\n                    _c(\n                      \"div\",\n                      {\n                        staticClass: \"num_value\",\n                        style: `color:${item.color}`,\n                      },\n                      [_vm._v(_vm._s(item.value))]\n                    ),\n                  ]),\n                ])\n              }),\n              0\n            ),\n            _c(\n              \"div\",\n              { staticClass: \"committee_statistics_chart\" },\n              [\n                _c(\"BarScrollChart\", {\n                  attrs: {\n                    id: \"committee-statistics\",\n                    \"chart-data\": _vm.committeeBarData,\n                  },\n                }),\n              ],\n              1\n            ),\n          ]),\n        ]),\n        _c(\"div\", { staticClass: \"proposal_statistics\" }, [\n          _vm._m(2),\n          _c(\"div\", { staticClass: \"proposal_statistics_content\" }, [\n            _c(\n              \"div\",\n              { staticClass: \"proposal_statistics_num\" },\n              _vm._l(_vm.proposalStatisticsNum, function (item, index) {\n                return _c(\"div\", { key: index, staticClass: \"num_box\" }, [\n                  _c(\"img\", {\n                    staticClass: \"num_icon\",\n                    attrs: { src: item.icon, alt: \"\" },\n                  }),\n                  _c(\"div\", [\n                    _c(\"div\", { staticClass: \"num_label\" }, [\n                      _vm._v(_vm._s(item.label)),\n                    ]),\n                    _c(\n                      \"div\",\n                      {\n                        staticClass: \"num_value\",\n                        style: `color:${item.color}`,\n                      },\n                      [\n                        _vm._v(_vm._s(item.value)),\n                        _c(\"span\", { staticClass: \"num_unit\" }, [\n                          _vm._v(_vm._s(item.unit)),\n                        ]),\n                      ]\n                    ),\n                  ]),\n                ])\n              }),\n              0\n            ),\n            _c(\n              \"div\",\n              { staticClass: \"proposal_statistics_chart\" },\n              [\n                _c(\"PieChart\", {\n                  attrs: {\n                    id: \"proposal-statistics\",\n                    \"chart-data\": _vm.proposalChartData,\n                    name: _vm.proposalChartName,\n                  },\n                }),\n              ],\n              1\n            ),\n          ]),\n        ]),\n        _c(\"div\", { staticClass: \"work_dynamics\" }, [\n          _vm._m(3),\n          _c(\"div\", { staticClass: \"work_dynamics_content\" }, [\n            _c(\n              \"div\",\n              { staticClass: \"dynamics-list\" },\n              _vm._l(_vm.workDynamicsData, function (item, index) {\n                return _c(\n                  \"div\",\n                  {\n                    key: item.id,\n                    staticClass: \"dynamics-item\",\n                    class: {\n                      \"with-bg-image\": index % 2 === 0,\n                      \"with-bg-color\": index % 2 === 1,\n                    },\n                  },\n                  [\n                    _c(\"div\", { staticClass: \"dynamics-content\" }, [\n                      _c(\"div\", { staticClass: \"dynamics-title\" }, [\n                        _vm._v(_vm._s(item.title)),\n                      ]),\n                      _c(\"div\", { staticClass: \"dynamics-date\" }, [\n                        _vm._v(_vm._s(item.date)),\n                      ]),\n                    ]),\n                  ]\n                )\n              }),\n              0\n            ),\n          ]),\n        ]),\n      ]),\n      _c(\"div\", { staticClass: \"center-panel\" }, [\n        _c(\n          \"div\",\n          { staticClass: \"map_box\" },\n          [\n            _c(\"MapComponent\", {\n              attrs: {\n                data: _vm.mapData,\n                areaId: _vm.areaId + \"\",\n                areaName: _vm.areaName,\n              },\n              on: { \"region-click\": _vm.handleRegionClick },\n            }),\n          ],\n          1\n        ),\n        _c(\"div\", { staticClass: \"performance_statistics\" }, [\n          _vm._m(4),\n          _c(\"div\", { staticClass: \"performance_statistics_content\" }, [\n            _c(\"div\", { staticClass: \"table-container\" }, [\n              _vm._m(5),\n              _c(\n                \"div\",\n                { staticClass: \"table-body\" },\n                _vm._l(_vm.performanceData, function (item, index) {\n                  return _c(\"div\", { key: index, staticClass: \"table-row\" }, [\n                    _c(\"div\", { staticClass: \"table-cell name-col\" }, [\n                      _vm._v(_vm._s(item.name)),\n                    ]),\n                    _c(\"div\", { staticClass: \"table-cell meeting-col\" }, [\n                      _vm._v(_vm._s(item.meeting)),\n                    ]),\n                    _c(\"div\", { staticClass: \"table-cell proposal-col\" }, [\n                      _vm._v(_vm._s(item.proposal)),\n                    ]),\n                    _c(\"div\", { staticClass: \"table-cell opinion-col\" }, [\n                      _vm._v(_vm._s(item.opinion)),\n                    ]),\n                    _c(\"div\", { staticClass: \"table-cell suggestion-col\" }, [\n                      _vm._v(_vm._s(item.suggestion) + \" \"),\n                    ]),\n                    _c(\"div\", { staticClass: \"table-cell reading-col\" }, [\n                      _vm._v(_vm._s(item.reading)),\n                    ]),\n                    _c(\"div\", { staticClass: \"table-cell training-col\" }, [\n                      _vm._v(_vm._s(item.training)),\n                    ]),\n                  ])\n                }),\n                0\n              ),\n            ]),\n          ]),\n        ]),\n      ]),\n      _c(\"div\", { staticClass: \"right-panel\" }, [\n        _c(\"div\", { staticClass: \"social\" }, [\n          _vm._m(6),\n          _c(\"div\", { staticClass: \"social_content\" }, [\n            _c(\"div\", { staticClass: \"social-data-container\" }, [\n              _c(\"div\", { staticClass: \"left-data-item\" }, [\n                _c(\"div\", { staticClass: \"left-data-label\" }, [\n                  _vm._v(\"委员报送\"),\n                ]),\n                _c(\"div\", { staticClass: \"left-data-value\" }, [\n                  _vm._v(\"总数\"),\n                  _c(\"span\", [\n                    _vm._v(_vm._s(_vm.socialData.memberSubmit.count)),\n                  ]),\n                  _vm._v(\"篇\"),\n                ]),\n                _c(\"div\", { staticClass: \"left-data-detail\" }, [\n                  _vm._v(\"采用\"),\n                  _c(\"span\", [\n                    _vm._v(_vm._s(_vm.socialData.memberSubmit.adopted)),\n                  ]),\n                  _vm._v(\" 篇\"),\n                ]),\n              ]),\n              _c(\"div\", { staticClass: \"center-chart\" }, [\n                _c(\"div\", { staticClass: \"progress-content\" }, [\n                  _c(\"div\", { staticClass: \"total-number\" }, [\n                    _vm._v(_vm._s(_vm.socialData.total)),\n                  ]),\n                  _c(\"div\", { staticClass: \"total-label\" }, [_vm._v(\"总数\")]),\n                ]),\n              ]),\n              _c(\"div\", { staticClass: \"right-data-item\" }, [\n                _c(\"div\", { staticClass: \"right-data-label\" }, [\n                  _vm._v(\"单位报送\"),\n                ]),\n                _c(\"div\", { staticClass: \"right-data-value\" }, [\n                  _vm._v(\"总数\"),\n                  _c(\"span\", [_vm._v(_vm._s(_vm.socialData.unitSubmit.count))]),\n                  _vm._v(\"篇\"),\n                ]),\n                _c(\"div\", { staticClass: \"right-data-detail\" }, [\n                  _vm._v(\"采用\"),\n                  _c(\"span\", [\n                    _vm._v(_vm._s(_vm.socialData.unitSubmit.adopted)),\n                  ]),\n                  _vm._v(\" 篇\"),\n                ]),\n              ]),\n            ]),\n          ]),\n        ]),\n        _c(\"div\", { staticClass: \"conference_activities\" }, [\n          _vm._m(7),\n          _c(\"div\", { staticClass: \"conference_activities_content\" }, [\n            _c(\n              \"div\",\n              { staticClass: \"activities-grid\" },\n              _vm._l(_vm.conferenceActivitiesData, function (item, index) {\n                return _c(\n                  \"div\",\n                  {\n                    key: index,\n                    staticClass: \"activity-item\",\n                    class: _vm.getItemClass(item.name),\n                  },\n                  [\n                    _c(\"div\", { staticClass: \"activity-value\" }, [\n                      _vm._v(_vm._s(item.value)),\n                    ]),\n                    _c(\"div\", { staticClass: \"activity-name\" }, [\n                      _vm._v(_vm._s(item.name)),\n                    ]),\n                  ]\n                )\n              }),\n              0\n            ),\n          ]),\n        ]),\n        _c(\"div\", { staticClass: \"discussions\" }, [\n          _vm._m(8),\n          _c(\"div\", { staticClass: \"discussions_content\" }, [\n            _c(\n              \"div\",\n              { staticClass: \"statistics-section\" },\n              _vm._l(_vm.discussionsData.statistics, function (item, index) {\n                return _c(\"div\", { key: index, staticClass: \"stat-item\" }, [\n                  _c(\"div\", { staticClass: \"stat-dot\" }),\n                  _c(\"div\", { staticClass: \"stat-info\" }, [\n                    _c(\"span\", { staticClass: \"stat-name\" }, [\n                      _vm._v(_vm._s(item.name)),\n                    ]),\n                    _c(\"span\", { staticClass: \"stat-value\" }, [\n                      _vm._v(_vm._s(item.value)),\n                    ]),\n                    _c(\"span\", { staticClass: \"stat-unit\" }, [\n                      _vm._v(_vm._s(item.unit)),\n                    ]),\n                  ]),\n                ])\n              }),\n              0\n            ),\n            _c(\"div\", { staticClass: \"hot-topics-section\" }, [\n              _vm._m(9),\n              _c(\n                \"div\",\n                { staticClass: \"topics-list\" },\n                _vm._l(_vm.discussionsData.hotTopics, function (topic, index) {\n                  return _c(\"div\", { key: index, staticClass: \"topic-item\" }, [\n                    _c(\"div\", { staticClass: \"topic-dot\" }),\n                    _c(\"span\", { staticClass: \"topic-text\" }, [\n                      _vm._v(_vm._s(topic)),\n                    ]),\n                  ])\n                }),\n                0\n              ),\n            ]),\n          ]),\n        ]),\n      ]),\n    ]),\n  ])\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"header-center\" }, [\n      _c(\"img\", {\n        staticStyle: { height: \"50px\" },\n        attrs: {\n          src: require(\"../../../assets/largeScreen/top_header_txt.png\"),\n          alt: \"\",\n        },\n      }),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"header_box\" }, [\n      _c(\"span\", { staticClass: \"header_text_left\" }, [_vm._v(\"委员统计\")]),\n      _c(\"span\", { staticClass: \"header_text_right\" }, [_vm._v(\"十二届二次\")]),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"header_box\" }, [\n      _c(\"span\", { staticClass: \"header_text_left\" }, [_vm._v(\"提案统计\")]),\n      _c(\"span\", { staticClass: \"header_text_right\" }, [\n        _vm._v(\"十二届二次会议\"),\n      ]),\n      _c(\"span\", { staticClass: \"header_text_center\" }, [\n        _vm._v(\"提交提案总数：\"),\n        _c(\"span\", [_vm._v(\"873\")]),\n        _vm._v(\"件\"),\n      ]),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"header_box\" }, [\n      _c(\"span\", { staticClass: \"header_text_left\" }, [_vm._v(\"工作动态\")]),\n      _c(\"span\", { staticClass: \"header_text_right\" }, [_vm._v(\"本年\")]),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"header_box\" }, [\n      _c(\"span\", { staticClass: \"header_text_left\" }, [_vm._v(\"履职统计\")]),\n      _c(\"span\", { staticClass: \"header_text_right\" }, [_vm._v(\"十二届二次\")]),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"table-header\" }, [\n      _c(\"div\", { staticClass: \"header-cell\" }, [_vm._v(\"姓名\")]),\n      _c(\"div\", { staticClass: \"header-cell\" }, [_vm._v(\"会议活动\")]),\n      _c(\"div\", { staticClass: \"header-cell\" }, [_vm._v(\"政协提案\")]),\n      _c(\"div\", { staticClass: \"header-cell\" }, [_vm._v(\"社情民意\")]),\n      _c(\"div\", { staticClass: \"header-cell\" }, [_vm._v(\"议政建言\")]),\n      _c(\"div\", { staticClass: \"header-cell\" }, [_vm._v(\"读书心得\")]),\n      _c(\"div\", { staticClass: \"header-cell\" }, [_vm._v(\"委员培训\")]),\n      _c(\"div\", { staticClass: \"header-cell\" }),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"header_box\" }, [\n      _c(\"span\", { staticClass: \"header_text_left\" }, [_vm._v(\"社情民意\")]),\n      _c(\"span\", { staticClass: \"header_text_right\" }, [_vm._v(\"本年\")]),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"header_box\" }, [\n      _c(\"span\", { staticClass: \"header_text_left\" }, [_vm._v(\"会议活动\")]),\n      _c(\"span\", { staticClass: \"header_text_right\" }, [_vm._v(\"本年\")]),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"header_box\" }, [\n      _c(\"span\", { staticClass: \"header_text_left\" }, [_vm._v(\"网络议政\")]),\n      _c(\"span\", { staticClass: \"header_text_right\" }),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"hot-topics-header\" }, [\n      _c(\"img\", {\n        staticClass: \"hot-icon\",\n        attrs: {\n          src: require(\"../../../assets/largeScreen/icon_hot.png\"),\n          alt: \"热门\",\n        },\n      }),\n      _c(\"span\", { staticClass: \"hot-title\" }, [_vm._v(\"最热话题\")]),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAT,GAAkB;EAC7B,IAAIC,GAAG,GAAG,IAAV;EAAA,IACEC,EAAE,GAAGD,GAAG,CAACE,KAAJ,CAAUD,EADjB;;EAEA,OAAOA,EAAE,CAAC,KAAD,EAAQ;IAAEE,GAAG,EAAE,WAAP;IAAoBC,WAAW,EAAE;EAAjC,CAAR,EAAyD,CAChEH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA0C,CAC1CH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwC,CACxCH,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAAuC,CACvCJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAON,GAAG,CAACO,WAAX,CAAP,CADuC,CAAvC,CADsC,EAIxCN,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAAqC,CAACJ,GAAG,CAACK,EAAJ,CAAO,WAAP,CAAD,CAArC,CAJsC,CAAxC,CADwC,EAO1CL,GAAG,CAACQ,EAAJ,CAAO,CAAP,CAP0C,EAQ1CP,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,CARwC,CAA1C,CAD8D,EAWhEH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA2C,CAC3CH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAuC,CACvCH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAiD,CACjDJ,GAAG,CAACQ,EAAJ,CAAO,CAAP,CADiD,EAEjDP,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAyD,CACzDH,EAAE,CACA,KADA,EAEA;IAAEG,WAAW,EAAE;EAAf,CAFA,EAGAJ,GAAG,CAACS,EAAJ,CAAOT,GAAG,CAACU,sBAAX,EAAmC,UAAUC,IAAV,EAAgBC,KAAhB,EAAuB;IACxD,OAAOX,EAAE,CAAC,KAAD,EAAQ;MAAEY,GAAG,EAAED,KAAP;MAAcR,WAAW,EAAE;IAA3B,CAAR,EAAgD,CACvDH,EAAE,CAAC,KAAD,EAAQ;MACRG,WAAW,EAAE,UADL;MAERU,KAAK,EAAE;QAAEC,GAAG,EAAEJ,IAAI,CAACK,IAAZ;QAAkBC,GAAG,EAAE;MAAvB;IAFC,CAAR,CADqD,EAKvDhB,EAAE,CAAC,KAAD,EAAQ,CACRA,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAAsC,CACtCJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOK,IAAI,CAACO,KAAZ,CAAP,CADsC,CAAtC,CADM,EAIRjB,EAAE,CACA,KADA,EAEA;MACEG,WAAW,EAAE,WADf;MAEEe,KAAK,EAAG,SAAQR,IAAI,CAACS,KAAM;IAF7B,CAFA,EAMA,CAACpB,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOK,IAAI,CAACU,KAAZ,CAAP,CAAD,CANA,CAJM,CAAR,CALqD,CAAhD,CAAT;EAmBD,CApBD,CAHA,EAwBA,CAxBA,CADuD,EA2BzDpB,EAAE,CACA,KADA,EAEA;IAAEG,WAAW,EAAE;EAAf,CAFA,EAGA,CACEH,EAAE,CAAC,gBAAD,EAAmB;IACnBa,KAAK,EAAE;MACLQ,EAAE,EAAE,sBADC;MAEL,cAActB,GAAG,CAACuB;IAFb;EADY,CAAnB,CADJ,CAHA,EAWA,CAXA,CA3BuD,CAAzD,CAF+C,CAAjD,CADqC,EA6CvCtB,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAgD,CAChDJ,GAAG,CAACQ,EAAJ,CAAO,CAAP,CADgD,EAEhDP,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwD,CACxDH,EAAE,CACA,KADA,EAEA;IAAEG,WAAW,EAAE;EAAf,CAFA,EAGAJ,GAAG,CAACS,EAAJ,CAAOT,GAAG,CAACwB,qBAAX,EAAkC,UAAUb,IAAV,EAAgBC,KAAhB,EAAuB;IACvD,OAAOX,EAAE,CAAC,KAAD,EAAQ;MAAEY,GAAG,EAAED,KAAP;MAAcR,WAAW,EAAE;IAA3B,CAAR,EAAgD,CACvDH,EAAE,CAAC,KAAD,EAAQ;MACRG,WAAW,EAAE,UADL;MAERU,KAAK,EAAE;QAAEC,GAAG,EAAEJ,IAAI,CAACK,IAAZ;QAAkBC,GAAG,EAAE;MAAvB;IAFC,CAAR,CADqD,EAKvDhB,EAAE,CAAC,KAAD,EAAQ,CACRA,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAAsC,CACtCJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOK,IAAI,CAACO,KAAZ,CAAP,CADsC,CAAtC,CADM,EAIRjB,EAAE,CACA,KADA,EAEA;MACEG,WAAW,EAAE,WADf;MAEEe,KAAK,EAAG,SAAQR,IAAI,CAACS,KAAM;IAF7B,CAFA,EAMA,CACEpB,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOK,IAAI,CAACU,KAAZ,CAAP,CADF,EAEEpB,EAAE,CAAC,MAAD,EAAS;MAAEG,WAAW,EAAE;IAAf,CAAT,EAAsC,CACtCJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOK,IAAI,CAACc,IAAZ,CAAP,CADsC,CAAtC,CAFJ,CANA,CAJM,CAAR,CALqD,CAAhD,CAAT;EAwBD,CAzBD,CAHA,EA6BA,CA7BA,CADsD,EAgCxDxB,EAAE,CACA,KADA,EAEA;IAAEG,WAAW,EAAE;EAAf,CAFA,EAGA,CACEH,EAAE,CAAC,UAAD,EAAa;IACba,KAAK,EAAE;MACLQ,EAAE,EAAE,qBADC;MAEL,cAActB,GAAG,CAAC0B,iBAFb;MAGLC,IAAI,EAAE3B,GAAG,CAAC4B;IAHL;EADM,CAAb,CADJ,CAHA,EAYA,CAZA,CAhCsD,CAAxD,CAF8C,CAAhD,CA7CqC,EA+FvC3B,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA0C,CAC1CJ,GAAG,CAACQ,EAAJ,CAAO,CAAP,CAD0C,EAE1CP,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAkD,CAClDH,EAAE,CACA,KADA,EAEA;IAAEG,WAAW,EAAE;EAAf,CAFA,EAGAJ,GAAG,CAACS,EAAJ,CAAOT,GAAG,CAAC6B,gBAAX,EAA6B,UAAUlB,IAAV,EAAgBC,KAAhB,EAAuB;IAClD,OAAOX,EAAE,CACP,KADO,EAEP;MACEY,GAAG,EAAEF,IAAI,CAACW,EADZ;MAEElB,WAAW,EAAE,eAFf;MAGE0B,KAAK,EAAE;QACL,iBAAiBlB,KAAK,GAAG,CAAR,KAAc,CAD1B;QAEL,iBAAiBA,KAAK,GAAG,CAAR,KAAc;MAF1B;IAHT,CAFO,EAUP,CACEX,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAA6C,CAC7CH,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAA2C,CAC3CJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOK,IAAI,CAACoB,KAAZ,CAAP,CAD2C,CAA3C,CAD2C,EAI7C9B,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAA0C,CAC1CJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOK,IAAI,CAACqB,IAAZ,CAAP,CAD0C,CAA1C,CAJ2C,CAA7C,CADJ,CAVO,CAAT;EAqBD,CAtBD,CAHA,EA0BA,CA1BA,CADgD,CAAlD,CAFwC,CAA1C,CA/FqC,CAAvC,CADyC,EAkI3C/B,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAyC,CACzCH,EAAE,CACA,KADA,EAEA;IAAEG,WAAW,EAAE;EAAf,CAFA,EAGA,CACEH,EAAE,CAAC,cAAD,EAAiB;IACjBa,KAAK,EAAE;MACLmB,IAAI,EAAEjC,GAAG,CAACkC,OADL;MAELC,MAAM,EAAEnC,GAAG,CAACmC,MAAJ,GAAa,EAFhB;MAGLC,QAAQ,EAAEpC,GAAG,CAACoC;IAHT,CADU;IAMjBC,EAAE,EAAE;MAAE,gBAAgBrC,GAAG,CAACsC;IAAtB;EANa,CAAjB,CADJ,CAHA,EAaA,CAbA,CADuC,EAgBzCrC,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAmD,CACnDJ,GAAG,CAACQ,EAAJ,CAAO,CAAP,CADmD,EAEnDP,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA2D,CAC3DH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA4C,CAC5CJ,GAAG,CAACQ,EAAJ,CAAO,CAAP,CAD4C,EAE5CP,EAAE,CACA,KADA,EAEA;IAAEG,WAAW,EAAE;EAAf,CAFA,EAGAJ,GAAG,CAACS,EAAJ,CAAOT,GAAG,CAACuC,eAAX,EAA4B,UAAU5B,IAAV,EAAgBC,KAAhB,EAAuB;IACjD,OAAOX,EAAE,CAAC,KAAD,EAAQ;MAAEY,GAAG,EAAED,KAAP;MAAcR,WAAW,EAAE;IAA3B,CAAR,EAAkD,CACzDH,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAAgD,CAChDJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOK,IAAI,CAACgB,IAAZ,CAAP,CADgD,CAAhD,CADuD,EAIzD1B,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAAmD,CACnDJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOK,IAAI,CAAC6B,OAAZ,CAAP,CADmD,CAAnD,CAJuD,EAOzDvC,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAAoD,CACpDJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOK,IAAI,CAAC8B,QAAZ,CAAP,CADoD,CAApD,CAPuD,EAUzDxC,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAAmD,CACnDJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOK,IAAI,CAAC+B,OAAZ,CAAP,CADmD,CAAnD,CAVuD,EAazDzC,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAAsD,CACtDJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOK,IAAI,CAACgC,UAAZ,IAA0B,GAAjC,CADsD,CAAtD,CAbuD,EAgBzD1C,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAAmD,CACnDJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOK,IAAI,CAACiC,OAAZ,CAAP,CADmD,CAAnD,CAhBuD,EAmBzD3C,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAAoD,CACpDJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOK,IAAI,CAACkC,QAAZ,CAAP,CADoD,CAApD,CAnBuD,CAAlD,CAAT;EAuBD,CAxBD,CAHA,EA4BA,CA5BA,CAF0C,CAA5C,CADyD,CAA3D,CAFiD,CAAnD,CAhBuC,CAAzC,CAlIyC,EAyL3C5C,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwC,CACxCH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAmC,CACnCJ,GAAG,CAACQ,EAAJ,CAAO,CAAP,CADmC,EAEnCP,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA2C,CAC3CH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAkD,CAClDH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA2C,CAC3CH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA4C,CAC5CJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAD4C,CAA5C,CADyC,EAI3CJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA4C,CAC5CJ,GAAG,CAACK,EAAJ,CAAO,IAAP,CAD4C,EAE5CJ,EAAE,CAAC,MAAD,EAAS,CACTD,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAON,GAAG,CAAC8C,UAAJ,CAAeC,YAAf,CAA4BC,KAAnC,CAAP,CADS,CAAT,CAF0C,EAK5ChD,GAAG,CAACK,EAAJ,CAAO,GAAP,CAL4C,CAA5C,CAJyC,EAW3CJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA6C,CAC7CJ,GAAG,CAACK,EAAJ,CAAO,IAAP,CAD6C,EAE7CJ,EAAE,CAAC,MAAD,EAAS,CACTD,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAON,GAAG,CAAC8C,UAAJ,CAAeC,YAAf,CAA4BE,OAAnC,CAAP,CADS,CAAT,CAF2C,EAK7CjD,GAAG,CAACK,EAAJ,CAAO,IAAP,CAL6C,CAA7C,CAXyC,CAA3C,CADgD,EAoBlDJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAyC,CACzCH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA6C,CAC7CH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAyC,CACzCJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAON,GAAG,CAAC8C,UAAJ,CAAeI,KAAtB,CAAP,CADyC,CAAzC,CAD2C,EAI7CjD,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwC,CAACJ,GAAG,CAACK,EAAJ,CAAO,IAAP,CAAD,CAAxC,CAJ2C,CAA7C,CADuC,CAAzC,CApBgD,EA4BlDJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA4C,CAC5CH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA6C,CAC7CJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAD6C,CAA7C,CAD0C,EAI5CJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA6C,CAC7CJ,GAAG,CAACK,EAAJ,CAAO,IAAP,CAD6C,EAE7CJ,EAAE,CAAC,MAAD,EAAS,CAACD,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAON,GAAG,CAAC8C,UAAJ,CAAeK,UAAf,CAA0BH,KAAjC,CAAP,CAAD,CAAT,CAF2C,EAG7ChD,GAAG,CAACK,EAAJ,CAAO,GAAP,CAH6C,CAA7C,CAJ0C,EAS5CJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA8C,CAC9CJ,GAAG,CAACK,EAAJ,CAAO,IAAP,CAD8C,EAE9CJ,EAAE,CAAC,MAAD,EAAS,CACTD,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAON,GAAG,CAAC8C,UAAJ,CAAeK,UAAf,CAA0BF,OAAjC,CAAP,CADS,CAAT,CAF4C,EAK9CjD,GAAG,CAACK,EAAJ,CAAO,IAAP,CAL8C,CAA9C,CAT0C,CAA5C,CA5BgD,CAAlD,CADyC,CAA3C,CAFiC,CAAnC,CADsC,EAoDxCJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAkD,CAClDJ,GAAG,CAACQ,EAAJ,CAAO,CAAP,CADkD,EAElDP,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA0D,CAC1DH,EAAE,CACA,KADA,EAEA;IAAEG,WAAW,EAAE;EAAf,CAFA,EAGAJ,GAAG,CAACS,EAAJ,CAAOT,GAAG,CAACoD,wBAAX,EAAqC,UAAUzC,IAAV,EAAgBC,KAAhB,EAAuB;IAC1D,OAAOX,EAAE,CACP,KADO,EAEP;MACEY,GAAG,EAAED,KADP;MAEER,WAAW,EAAE,eAFf;MAGE0B,KAAK,EAAE9B,GAAG,CAACqD,YAAJ,CAAiB1C,IAAI,CAACgB,IAAtB;IAHT,CAFO,EAOP,CACE1B,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAA2C,CAC3CJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOK,IAAI,CAACU,KAAZ,CAAP,CAD2C,CAA3C,CADJ,EAIEpB,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAA0C,CAC1CJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOK,IAAI,CAACgB,IAAZ,CAAP,CAD0C,CAA1C,CAJJ,CAPO,CAAT;EAgBD,CAjBD,CAHA,EAqBA,CArBA,CADwD,CAA1D,CAFgD,CAAlD,CApDsC,EAgFxC1B,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwC,CACxCJ,GAAG,CAACQ,EAAJ,CAAO,CAAP,CADwC,EAExCP,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAgD,CAChDH,EAAE,CACA,KADA,EAEA;IAAEG,WAAW,EAAE;EAAf,CAFA,EAGAJ,GAAG,CAACS,EAAJ,CAAOT,GAAG,CAACsD,eAAJ,CAAoBC,UAA3B,EAAuC,UAAU5C,IAAV,EAAgBC,KAAhB,EAAuB;IAC5D,OAAOX,EAAE,CAAC,KAAD,EAAQ;MAAEY,GAAG,EAAED,KAAP;MAAcR,WAAW,EAAE;IAA3B,CAAR,EAAkD,CACzDH,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,CADuD,EAEzDH,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAAsC,CACtCH,EAAE,CAAC,MAAD,EAAS;MAAEG,WAAW,EAAE;IAAf,CAAT,EAAuC,CACvCJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOK,IAAI,CAACgB,IAAZ,CAAP,CADuC,CAAvC,CADoC,EAItC1B,EAAE,CAAC,MAAD,EAAS;MAAEG,WAAW,EAAE;IAAf,CAAT,EAAwC,CACxCJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOK,IAAI,CAACU,KAAZ,CAAP,CADwC,CAAxC,CAJoC,EAOtCpB,EAAE,CAAC,MAAD,EAAS;MAAEG,WAAW,EAAE;IAAf,CAAT,EAAuC,CACvCJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOK,IAAI,CAACc,IAAZ,CAAP,CADuC,CAAvC,CAPoC,CAAtC,CAFuD,CAAlD,CAAT;EAcD,CAfD,CAHA,EAmBA,CAnBA,CAD8C,EAsBhDxB,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA+C,CAC/CJ,GAAG,CAACQ,EAAJ,CAAO,CAAP,CAD+C,EAE/CP,EAAE,CACA,KADA,EAEA;IAAEG,WAAW,EAAE;EAAf,CAFA,EAGAJ,GAAG,CAACS,EAAJ,CAAOT,GAAG,CAACsD,eAAJ,CAAoBE,SAA3B,EAAsC,UAAUC,KAAV,EAAiB7C,KAAjB,EAAwB;IAC5D,OAAOX,EAAE,CAAC,KAAD,EAAQ;MAAEY,GAAG,EAAED,KAAP;MAAcR,WAAW,EAAE;IAA3B,CAAR,EAAmD,CAC1DH,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,CADwD,EAE1DH,EAAE,CAAC,MAAD,EAAS;MAAEG,WAAW,EAAE;IAAf,CAAT,EAAwC,CACxCJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOmD,KAAP,CAAP,CADwC,CAAxC,CAFwD,CAAnD,CAAT;EAMD,CAPD,CAHA,EAWA,CAXA,CAF6C,CAA/C,CAtB8C,CAAhD,CAFsC,CAAxC,CAhFsC,CAAxC,CAzLyC,CAA3C,CAX8D,CAAzD,CAAT;AAiUD,CApUD;;AAqUA,IAAIC,eAAe,GAAG,CACpB,YAAY;EACV,IAAI1D,GAAG,GAAG,IAAV;EAAA,IACEC,EAAE,GAAGD,GAAG,CAACE,KAAJ,CAAUD,EADjB;;EAEA,OAAOA,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA0C,CACjDH,EAAE,CAAC,KAAD,EAAQ;IACR0D,WAAW,EAAE;MAAEC,MAAM,EAAE;IAAV,CADL;IAER9C,KAAK,EAAE;MACLC,GAAG,EAAE8C,OAAO,CAAC,gDAAD,CADP;MAEL5C,GAAG,EAAE;IAFA;EAFC,CAAR,CAD+C,CAA1C,CAAT;AASD,CAbmB,EAcpB,YAAY;EACV,IAAIjB,GAAG,GAAG,IAAV;EAAA,IACEC,EAAE,GAAGD,GAAG,CAACE,KAAJ,CAAUD,EADjB;;EAEA,OAAOA,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAuC,CAC9CH,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAA8C,CAACJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CAA9C,CAD4C,EAE9CJ,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAA+C,CAACJ,GAAG,CAACK,EAAJ,CAAO,OAAP,CAAD,CAA/C,CAF4C,CAAvC,CAAT;AAID,CArBmB,EAsBpB,YAAY;EACV,IAAIL,GAAG,GAAG,IAAV;EAAA,IACEC,EAAE,GAAGD,GAAG,CAACE,KAAJ,CAAUD,EADjB;;EAEA,OAAOA,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAuC,CAC9CH,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAA8C,CAACJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CAA9C,CAD4C,EAE9CJ,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAA+C,CAC/CJ,GAAG,CAACK,EAAJ,CAAO,SAAP,CAD+C,CAA/C,CAF4C,EAK9CJ,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAAgD,CAChDJ,GAAG,CAACK,EAAJ,CAAO,SAAP,CADgD,EAEhDJ,EAAE,CAAC,MAAD,EAAS,CAACD,GAAG,CAACK,EAAJ,CAAO,KAAP,CAAD,CAAT,CAF8C,EAGhDL,GAAG,CAACK,EAAJ,CAAO,GAAP,CAHgD,CAAhD,CAL4C,CAAvC,CAAT;AAWD,CApCmB,EAqCpB,YAAY;EACV,IAAIL,GAAG,GAAG,IAAV;EAAA,IACEC,EAAE,GAAGD,GAAG,CAACE,KAAJ,CAAUD,EADjB;;EAEA,OAAOA,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAuC,CAC9CH,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAA8C,CAACJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CAA9C,CAD4C,EAE9CJ,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAA+C,CAACJ,GAAG,CAACK,EAAJ,CAAO,IAAP,CAAD,CAA/C,CAF4C,CAAvC,CAAT;AAID,CA5CmB,EA6CpB,YAAY;EACV,IAAIL,GAAG,GAAG,IAAV;EAAA,IACEC,EAAE,GAAGD,GAAG,CAACE,KAAJ,CAAUD,EADjB;;EAEA,OAAOA,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAuC,CAC9CH,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAA8C,CAACJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CAA9C,CAD4C,EAE9CJ,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAA+C,CAACJ,GAAG,CAACK,EAAJ,CAAO,OAAP,CAAD,CAA/C,CAF4C,CAAvC,CAAT;AAID,CApDmB,EAqDpB,YAAY;EACV,IAAIL,GAAG,GAAG,IAAV;EAAA,IACEC,EAAE,GAAGD,GAAG,CAACE,KAAJ,CAAUD,EADjB;;EAEA,OAAOA,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAyC,CAChDH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwC,CAACJ,GAAG,CAACK,EAAJ,CAAO,IAAP,CAAD,CAAxC,CAD8C,EAEhDJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwC,CAACJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CAAxC,CAF8C,EAGhDJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwC,CAACJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CAAxC,CAH8C,EAIhDJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwC,CAACJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CAAxC,CAJ8C,EAKhDJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwC,CAACJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CAAxC,CAL8C,EAMhDJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwC,CAACJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CAAxC,CAN8C,EAOhDJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwC,CAACJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CAAxC,CAP8C,EAQhDJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,CAR8C,CAAzC,CAAT;AAUD,CAlEmB,EAmEpB,YAAY;EACV,IAAIJ,GAAG,GAAG,IAAV;EAAA,IACEC,EAAE,GAAGD,GAAG,CAACE,KAAJ,CAAUD,EADjB;;EAEA,OAAOA,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAuC,CAC9CH,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAA8C,CAACJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CAA9C,CAD4C,EAE9CJ,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAA+C,CAACJ,GAAG,CAACK,EAAJ,CAAO,IAAP,CAAD,CAA/C,CAF4C,CAAvC,CAAT;AAID,CA1EmB,EA2EpB,YAAY;EACV,IAAIL,GAAG,GAAG,IAAV;EAAA,IACEC,EAAE,GAAGD,GAAG,CAACE,KAAJ,CAAUD,EADjB;;EAEA,OAAOA,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAuC,CAC9CH,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAA8C,CAACJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CAA9C,CAD4C,EAE9CJ,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAA+C,CAACJ,GAAG,CAACK,EAAJ,CAAO,IAAP,CAAD,CAA/C,CAF4C,CAAvC,CAAT;AAID,CAlFmB,EAmFpB,YAAY;EACV,IAAIL,GAAG,GAAG,IAAV;EAAA,IACEC,EAAE,GAAGD,GAAG,CAACE,KAAJ,CAAUD,EADjB;;EAEA,OAAOA,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAuC,CAC9CH,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAA8C,CAACJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CAA9C,CAD4C,EAE9CJ,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,CAF4C,CAAvC,CAAT;AAID,CA1FmB,EA2FpB,YAAY;EACV,IAAIJ,GAAG,GAAG,IAAV;EAAA,IACEC,EAAE,GAAGD,GAAG,CAACE,KAAJ,CAAUD,EADjB;;EAEA,OAAOA,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA8C,CACrDH,EAAE,CAAC,KAAD,EAAQ;IACRG,WAAW,EAAE,UADL;IAERU,KAAK,EAAE;MACLC,GAAG,EAAE8C,OAAO,CAAC,0CAAD,CADP;MAEL5C,GAAG,EAAE;IAFA;EAFC,CAAR,CADmD,EAQrDhB,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAAuC,CAACJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CAAvC,CARmD,CAA9C,CAAT;AAUD,CAxGmB,CAAtB;AA0GAN,MAAM,CAAC+D,aAAP,GAAuB,IAAvB;AAEA,SAAS/D,MAAT,EAAiB2D,eAAjB"}]}