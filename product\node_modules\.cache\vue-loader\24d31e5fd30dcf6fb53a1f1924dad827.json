{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\committeeStatistics\\committeeStatistics.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\committeeStatistics\\committeeStatistics.vue", "mtime": 1755586395194}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdjb21taXR0ZWVTdGF0aXN0aWNzJywNCiAgZGF0YSAoKSB7DQogICAgcmV0dXJuIHsNCiAgICB9DQogIH0sDQogIGFjdGl2YXRlZCAoKSB7DQogICAgdGhpcy5tYWtlU2NyZWVuKCkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIG1ha2VTY3JlZW4gKCkgew0KICAgICAgY29uc3Qgcm91dGVEYXRhID0gdGhpcy4kcm91dGVyLnJlc29sdmUoeyBwYXRoOiAnL2NvbW1pdHRlZVN0YXRpc3RpY3NCb3gnLCBxdWVyeTogeyByb3V0ZTogJy9jb21taXR0ZWVTdGF0aXN0aWNzQm94JyB9IH0pDQogICAgICB3aW5kb3cub3Blbihyb3V0ZURhdGEuaHJlZiwgJ19ibGFuaycpDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["committeeStatistics.vue"], "names": [], "mappings": ";AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "committeeStatistics.vue", "sourceRoot": "src/views/smartBrainLargeScreen/committeeStatistics", "sourcesContent": ["<template>\r\n  <div class=\"committeeStatistics\">\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'committeeStatistics',\r\n  data () {\r\n    return {\r\n    }\r\n  },\r\n  activated () {\r\n    this.makeScreen()\r\n  },\r\n  methods: {\r\n    makeScreen () {\r\n      const routeData = this.$router.resolve({ path: '/committeeStatisticsBox', query: { route: '/committeeStatisticsBox' } })\r\n      window.open(routeData.href, '_blank')\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}