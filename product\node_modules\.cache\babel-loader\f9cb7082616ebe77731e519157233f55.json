{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\committeeStatistics\\committeeStatistics.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\committeeStatistics\\committeeStatistics.vue", "mtime": 1755586395194}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdjb21taXR0ZWVTdGF0aXN0aWNzJywKCiAgZGF0YSgpIHsKICAgIHJldHVybiB7fTsKICB9LAoKICBhY3RpdmF0ZWQoKSB7CiAgICB0aGlzLm1ha2VTY3JlZW4oKTsKICB9LAoKICBtZXRob2RzOiB7CiAgICBtYWtlU2NyZWVuKCkgewogICAgICBjb25zdCByb3V0ZURhdGEgPSB0aGlzLiRyb3V0ZXIucmVzb2x2ZSh7CiAgICAgICAgcGF0aDogJy9jb21taXR0ZWVTdGF0aXN0aWNzQm94JywKICAgICAgICBxdWVyeTogewogICAgICAgICAgcm91dGU6ICcvY29tbWl0dGVlU3RhdGlzdGljc0JveCcKICAgICAgICB9CiAgICAgIH0pOwogICAgICB3aW5kb3cub3Blbihyb3V0ZURhdGEuaHJlZiwgJ19ibGFuaycpOwogICAgfQoKICB9Cn07"}, {"version": 3, "mappings": "AAKA;EACAA,2BADA;;EAEAC;IACA;EAEA,CALA;;EAMAC;IACA;EACA,CARA;;EASAC;IACAC;MACA;QAAAC;QAAAC;UAAAC;QAAA;MAAA;MACAC;IACA;;EAJA;AATA", "names": ["name", "data", "activated", "methods", "makeScreen", "path", "query", "route", "window"], "sourceRoot": "src/views/smartBrainLargeScreen/committeeStatistics", "sources": ["committeeStatistics.vue"], "sourcesContent": ["<template>\r\n  <div class=\"committeeStatistics\">\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'committeeStatistics',\r\n  data () {\r\n    return {\r\n    }\r\n  },\r\n  activated () {\r\n    this.makeScreen()\r\n  },\r\n  methods: {\r\n    makeScreen () {\r\n      const routeData = this.$router.resolve({ path: '/committeeStatisticsBox', query: { route: '/committeeStatisticsBox' } })\r\n      window.open(routeData.href, '_blank')\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}