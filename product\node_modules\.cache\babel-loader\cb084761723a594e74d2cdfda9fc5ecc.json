{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\MapComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\MapComponent.vue", "mtime": 1755575152664}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAOA;;AACAA,kC,CAAA;;;AAEA;EACAC,oBADA;EAEAC,qCAFA;;EAGAC;IACA;MACAC;QACAC;UACAC,eADA;UAEAC;YACAC;YACA;AACA;AACA;AACA,mBAHA;UAIA,CARA;UASAC,wCATA;UAUAC,sBAVA;UAWAC,cAXA;UAYAC,eAZA;UAaAC,qCAbA;UAcAC,cAdA;UAeAC;YACAC,gBADA;YAEAC,YAFA;YAGAC;UAHA;QAfA,CADA;QAsBAC;UACAC,WADA;UAEAC,4BAFA;UAGAC,iBAHA;UAIAC,WAJA;UAKAC;YACAb,cADA;YAEAD,mCAFA;YAGAG,mCAHA;YAIAC,aAJA;YAKAW,gBALA;YAMAC;UANA,CALA;UAaAC;YACAhB,cADA;YAEAD,mCAFA;YAGAG,oCAHA;YAIAC,aAJA;YAKAW,gBALA;YAMAC,iBANA;YAOAE;cACAC,cADA;cAEAC,MAFA;cAGAC,MAHA;cAIAC,MAJA;cAKAC,aACA;gBAAAC;gBAAAlB;cAAA,CADA,EAEA;gBAAAkB;gBAAAlB;cAAA,CAFA;YALA;UAPA;QAbA,CAtBA;QAsDAmB;MAtDA,CADA;MAyDAC,kBAzDA;MA0DAC;IA1DA;EA4DA,CAhEA;;EAiEAC;IACA;MACA;IACA,CAFA;EAGA,CArEA;;EAsEAC;IACAC;MACA;MACA;IACA,CAJA;;IAMA;MACA;MACA;;MACA;QACA;;QACA;UACA;;UACA;YACAC;cAAAC;YAAA;UACA;QACA;MACA;;MACA;IACA,CAnBA;;IAqBAC;MACA;MACA;MACAC;MACA;MACA;MACAC;MACAC,sBACA,0BADA;QAEAX;MAFA;MAIAW;MACAA;QAAA;;QACA;QACA,+HAFA,CAGA;;QACAA;UACAX,SACA,oBADA,EAEA,0EAFA;QADA;MAMA,CAVA;MAWAY;QACA;UACAD;QACA;MACA,CAJA;IAKA,CAjDA;;IAmDAE;MACA;MACA;MACA;IACA,CAvDA;;IAyDAC;MACA;QACApB,eADA;QAEAqB,uBAFA;QAGA/C,eAHA;QAIAgD,UAJA;QAKAC,oBALA;QAMAC,WANA;QAOAC;MAPA;IASA,CAnEA;;IAqEAC;MACA;QACAlC,4BADA;QAEAC,iBAFA;QAGArB,WAHA;QAIA4B,WAJA;QAKAT,WALA;QAMAiC,WANA;QAOAG,uBAPA;QAQAC;UACAN,UADA;UAEAlC,YAFA;UAGAyC,kBAHA;UAIA1C,aAJA;UAKAT;YACA;UACA,CAPA;UAQAoB;YACAwB,UADA;YAEApC;cAAAC;YAAA;UAFA;QARA,CARA;QAqBAO,WArBA;QAsBAC;UACAd,sBADA;UAEAC,cAFA;UAGAE,qCAHA;UAIAC,cAJA;UAKAW,gBALA;UAMAC,iBANA;UAOAE;YACAC,cADA;YAEAC,MAFA;YAGAC,MAHA;YAIAC,MAJA;YAKAC,aACA;cAAAC;cAAAlB;YAAA,CADA,EAEA;cAAAkB;cAAAlB;YAAA,CAFA,EAGA;cAAAkB;cAAAlB;YAAA,CAHA,EAIA;cAAAkB;cAAAlB;YAAA,CAJA;UALA;QAPA,CAtBA;QA0CAW;UACAH;YACAd,sBADA;YAEAG,qCAFA;YAGAC,cAHA;YAIAW,gBAJA;YAKAC,iBALA;YAMAf,cANA;YAOAiB;cACAC,cADA;cAEAC,MAFA;cAGAC,MAHA;cAIAC,MAJA;cAKAC,aACA;gBAAAC;gBAAAlB;cAAA,CADA,EAEA;gBAAAkB;gBAAAlB;cAAA,CAFA,EAGA;gBAAAkB;gBAAAlB;cAAA,CAHA,EAIA;gBAAAkB;gBAAAlB;cAAA,CAJA;YALA;UAPA;QADA;MA1CA;IAiEA;;EAvIA;AAtEA", "names": ["require", "name", "props", "data", "options", "tooltip", "trigger", "formatter", "console", "backgroundColor", "borderColor", "borderWidth", "borderRadius", "shadowColor", "<PERSON><PERSON><PERSON><PERSON>", "textStyle", "color", "fontSize", "fontFamily", "geo", "map", "layoutCenter", "layoutSize", "roam", "itemStyle", "shadowOffsetX", "shadowOffsetY", "emphasis", "areaColor", "type", "x", "y", "r", "colorStops", "offset", "series", "echartObjRef", "regionList", "mounted", "methods", "splitFileName", "geoJson", "features", "renderMap", "dom", "echarts", "echartObj", "window", "getOptions", "getSeriesData", "coordinateSystem", "show", "symbolSize", "geoIndex", "symbolOffset", "getMapSeries", "showLegendSymbol", "label", "position"], "sourceRoot": "src/views/smartBrainLargeScreen/components", "sources": ["MapComponent.vue"], "sourcesContent": ["<template>\n  <div class=\"map-container\">\n    <div class=\"map\" id=\"homeMap\"></div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\nrequire('echarts/theme/macarons') // 引入主题\n\nexport default {\n  name: 'MapComponent',\n  props: ['data', 'areaId', 'areaName'],\n  data () {\n    return {\n      options: {\n        tooltip: {\n          trigger: 'item',\n          formatter: function (params) {\n            console.log('params', params)\n            return `<div style=\"padding: 8px;\">\n              <div style=\"color: #00D4FF; margin-bottom: 4px;\">${params.name}</div>\n              <div style=\"color: #FFFFFF;\">${params.value || 0}</div>\n            </div>`\n          },\n          backgroundColor: 'rgba(0, 20, 40, 0.95)',\n          borderColor: '#00D4FF',\n          borderWidth: 2,\n          borderRadius: 8,\n          shadowColor: 'rgba(0, 212, 255, 0.3)',\n          shadowBlur: 10,\n          textStyle: {\n            color: '#FFFFFF',\n            fontSize: 11,\n            fontFamily: 'Microsoft YaHei, Arial, sans-serif'\n          }\n        },\n        geo: {\n          map: '智慧人大',\n          layoutCenter: ['50%', '50%'],\n          layoutSize: '90%',\n          roam: false,\n          itemStyle: {\n            borderWidth: 2,\n            borderColor: 'rgba(0, 181, 254, 1)',\n            shadowColor: 'rgba(0, 181, 254, 1)',\n            shadowBlur: 0,\n            shadowOffsetX: 0,\n            shadowOffsetY: 10\n          },\n          emphasis: {\n            borderWidth: 2,\n            borderColor: 'rgba(0, 181, 254, 1)',\n            shadowColor: 'rgba(0, 181, 254, .1)',\n            shadowBlur: 0,\n            shadowOffsetX: 0,\n            shadowOffsetY: 10,\n            areaColor: {\n              type: 'radial',\n              x: 0.5,\n              y: 0.1,\n              r: 0.9,\n              colorStops: [\n                { offset: 0, color: 'rgba(0, 181, 254, 0.6)' },\n                { offset: 1, color: 'rgba(0, 181, 254, 0.6)' }\n              ]\n            }\n          }\n        },\n        series: []\n      },\n      echartObjRef: null,\n      regionList: []\n    }\n  },\n  mounted () {\n    this.$nextTick(() => {\n      this.initMap()\n    })\n  },\n  methods: {\n    splitFileName (text) {\n      var pattern = /\\.{1}[a-z]{1,}$/\n      return text.slice(text.lastIndexOf('/') + 1, pattern.exec(text).index)\n    },\n\n    async initMap (id = this.areaId) {\n      const mapJson = await import('./qingdao.json')\n      let geoJson = mapJson.default\n      if (id && id !== '370200' && this.data) {\n        const area = this.data.find(a => a.areaId === id)\n        if (area) {\n          const feature = geoJson.features.find(f => f.properties && (f.properties.adcode === area.adcode))\n          if (feature) {\n            geoJson = { ...geoJson, features: [feature] }\n          }\n        }\n      }\n      this.renderMap(geoJson, id)\n    },\n\n    renderMap (JSONData, areaId = this.areaId) {\n      const dom = document.getElementById('homeMap')\n      if (!dom) return\n      dom.removeAttribute('_echarts_instance_')\n      const echartObj = echarts.init(dom, 'macarons')\n      this.echartObjRef = echartObj\n      echarts.registerMap('智慧人大', JSONData)\n      echartObj.setOption({\n        ...this.getOptions(areaId),\n        series: [this.getSeriesData(), this.getMapSeries(areaId)]\n      })\n      echartObj.off('click')\n      echartObj.on('click', (param) => {\n        // areaName.value = param.name\n        const area = this.data?.find(a => a.name === param.name)\n        // emit('mapClick', { name: param.name, areaId: area?.areaId, adcode: area?.adcode })\n        echartObj.setOption({\n          series: [\n            this.getSeriesData(),\n            this.getMapSeries(area?.areaId)\n          ]\n        })\n      })\n      window.addEventListener('resize', () => {\n        if (echartObj && echartObj.resize) {\n          echartObj.resize()\n        }\n      })\n    },\n\n    getOptions (areaId) {\n      this.options.geo.layoutSize = areaId === '370200' ? '90%' : '70%'\n      this.options.series = [this.getSeriesData(), this.getMapSeries(areaId)]\n      return this.options\n    },\n\n    getSeriesData () {\n      return {\n        type: 'scatter',\n        coordinateSystem: 'geo',\n        data: this.data,\n        show: true,\n        symbolSize: [20, 25],\n        geoIndex: 0,\n        symbolOffset: [0, -20]\n      }\n    },\n\n    getMapSeries () {\n      return {\n        layoutCenter: ['50%', '50%'],\n        layoutSize: '90%',\n        name: 'map',\n        type: 'map',\n        map: '智慧人大',\n        geoIndex: 2,\n        showLegendSymbol: false,\n        label: {\n          show: true,\n          fontSize: 14,\n          position: 'center',\n          color: '#fff',\n          formatter: function (name) {\n            return name.name.length > 6 ? name.name.substring(0, 5) + '\\n' + name.name.substring(5) : name.name\n          },\n          emphasis: {\n            show: true,\n            textStyle: { color: '#fff' }\n          }\n        },\n        roam: false,\n        itemStyle: {\n          borderColor: '#00B5FE',\n          borderWidth: 2,\n          shadowColor: 'rgba(0, 181, 254, 0.6)',\n          shadowBlur: 15,\n          shadowOffsetX: 0,\n          shadowOffsetY: 15,\n          areaColor: {\n            type: 'radial',\n            x: 0.4,\n            y: 0.3,\n            r: 0.8,\n            colorStops: [\n              { offset: 0, color: 'rgba(20, 80, 150, 0.7)' },\n              { offset: 0.4, color: 'rgba(15, 60, 120, 0.8)' },\n              { offset: 0.8, color: 'rgba(10, 40, 90, 0.9)' },\n              { offset: 1, color: 'rgba(8, 25, 60, 1)' }\n            ]\n          }\n        },\n        emphasis: {\n          itemStyle: {\n            borderColor: '#00EAFF',\n            shadowColor: 'rgba(0, 234, 255, 0.8)',\n            shadowBlur: 25,\n            shadowOffsetX: 0,\n            shadowOffsetY: 20,\n            borderWidth: 3,\n            areaColor: {\n              type: 'radial',\n              x: 0.4,\n              y: 0.3,\n              r: 0.8,\n              colorStops: [\n                { offset: 0, color: 'rgba(40, 120, 200, 0.8)' },\n                { offset: 0.4, color: 'rgba(25, 90, 160, 0.9)' },\n                { offset: 0.8, color: 'rgba(15, 60, 120, 1)' },\n                { offset: 1, color: 'rgba(10, 35, 80, 1)' }\n              ]\n            }\n          }\n        }\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.map-container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n}\n\n#homeMap {\n  width: 100%;\n  height: 100%;\n}\n</style>\n"]}]}