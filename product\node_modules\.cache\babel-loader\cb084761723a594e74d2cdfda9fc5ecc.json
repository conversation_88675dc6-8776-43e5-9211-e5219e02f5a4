{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\MapComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\MapComponent.vue", "mtime": 1755575396110}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAOA;;AACAA,kC,CAAA;;;AAEA;EACAC,oBADA;EAEAC,qCAFA;;EAGAC;IACA;MACAC;QACAC;UACAC,eADA;UAEAC;YACAC;YACA;YACA;AACA;AACA;AACA,mBAHA;UAIA,CATA;UAUAC,wCAVA;UAWAC,sBAXA;UAYAC,cAZA;UAaAC,eAbA;UAcAC,qCAdA;UAeAC,cAfA;UAgBAC;YACAC,gBADA;YAEAC,YAFA;YAGAC;UAHA;QAhBA,CADA;QAuBAC;UACAC,WADA;UAEAC,4BAFA;UAGAC,iBAHA;UAIAC,WAJA;UAKAC;YACAb,cADA;YAEAD,mCAFA;YAGAG,mCAHA;YAIAC,aAJA;YAKAW,gBALA;YAMAC;UANA,CALA;UAaAC;YACAhB,cADA;YAEAD,mCAFA;YAGAG,oCAHA;YAIAC,aAJA;YAKAW,gBALA;YAMAC,iBANA;YAOAE;cACAC,cADA;cAEAC,MAFA;cAGAC,MAHA;cAIAC,MAJA;cAKAC,aACA;gBAAAC;gBAAAlB;cAAA,CADA,EAEA;gBAAAkB;gBAAAlB;cAAA,CAFA;YALA;UAPA;QAbA,CAvBA;QAuDAmB;MAvDA,CADA;MA0DAC,kBA1DA;MA2DAC;IA3DA;EA6DA,CAjEA;;EAkEAC;IACA;MACA;IACA,CAFA;EAGA,CAtEA;;EAuEAC;IACAC;MACA;MACA;IACA,CAJA;;IAMA;MACA;MACA;;MACA;QACA;;QACA;UACA;;UACA;YACAC;cAAAC;YAAA;UACA;QACA;MACA;;MACA;IACA,CAnBA;;IAqBAC;MACA;MACA;MACAC;MACA;MACA;MACAC;MACAC,sBACA,0BADA;QAEAX;MAFA;MAIAW;MACAA;QAAA;;QACA;QACA,+HAFA,CAGA;;QACAA;UACAX,SACA,oBADA,EAEA,0EAFA;QADA;MAMA,CAVA;MAWAY;QACA;UACAD;QACA;MACA,CAJA;IAKA,CAjDA;;IAmDAE;MACA;MACA;MACA;IACA,CAvDA;;IAyDAC;MACA;QACApB,eADA;QAEAqB,uBAFA;QAGA/C,eAHA;QAIAgD,UAJA;QAKAC,oBALA;QAMAC,WANA;QAOAC;MAPA;IASA,CAnEA;;IAqEAC;MACA;MACA;QACAtD,eADA;QAEAuD,8BAFA,CAEA;;MAFA,MAGA,EAHA;MAKA;QACAnC,4BADA;QAEAC,iBAFA;QAGArB,WAHA;QAIA4B,WAJA;QAKAT,WALA;QAMAiC,WANA;QAOAI,uBAPA;QAQAtD,aARA;QASAuD;UACAP,UADA;UAEAlC,YAFA;UAGA0C,kBAHA;UAIA3C,aAJA;UAKAT;YACA;UACA,CAPA;UAQAoB;YACAwB,UADA;YAEApC;cAAAC;YAAA;UAFA;QARA,CATA;QAsBAO,WAtBA;QAuBAC;UACAd,sBADA;UAEAC,cAFA;UAGAE,qCAHA;UAIAC,cAJA;UAKAW,gBALA;UAMAC,iBANA;UAOAE;YACAC,cADA;YAEAC,MAFA;YAGAC,MAHA;YAIAC,MAJA;YAKAC,aACA;cAAAC;cAAAlB;YAAA,CADA,EAEA;cAAAkB;cAAAlB;YAAA,CAFA,EAGA;cAAAkB;cAAAlB;YAAA,CAHA,EAIA;cAAAkB;cAAAlB;YAAA,CAJA;UALA;QAPA,CAvBA;QA2CAW;UACAH;YACAd,sBADA;YAEAG,qCAFA;YAGAC,cAHA;YAIAW,gBAJA;YAKAC,iBALA;YAMAf,cANA;YAOAiB;cACAC,cADA;cAEAC,MAFA;cAGAC,MAHA;cAIAC,MAJA;cAKAC,aACA;gBAAAC;gBAAAlB;cAAA,CADA,EAEA;gBAAAkB;gBAAAlB;cAAA,CAFA,EAGA;gBAAAkB;gBAAAlB;cAAA,CAHA,EAIA;gBAAAkB;gBAAAlB;cAAA,CAJA;YALA;UAPA;QADA;MA3CA;IAkEA;;EA9IA;AAvEA", "names": ["require", "name", "props", "data", "options", "tooltip", "trigger", "formatter", "console", "backgroundColor", "borderColor", "borderWidth", "borderRadius", "shadowColor", "<PERSON><PERSON><PERSON><PERSON>", "textStyle", "color", "fontSize", "fontFamily", "geo", "map", "layoutCenter", "layoutSize", "roam", "itemStyle", "shadowOffsetX", "shadowOffsetY", "emphasis", "areaColor", "type", "x", "y", "r", "colorStops", "offset", "series", "echartObjRef", "regionList", "mounted", "methods", "splitFileName", "geoJson", "features", "renderMap", "dom", "echarts", "echartObj", "window", "getOptions", "getSeriesData", "coordinateSystem", "show", "symbolSize", "geoIndex", "symbolOffset", "getMapSeries", "value", "showLegendSymbol", "label", "position"], "sourceRoot": "src/views/smartBrainLargeScreen/components", "sources": ["MapComponent.vue"], "sourcesContent": ["<template>\n  <div class=\"map-container\">\n    <div class=\"map\" id=\"homeMap\"></div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\nrequire('echarts/theme/macarons') // 引入主题\n\nexport default {\n  name: 'MapComponent',\n  props: ['data', 'areaId', 'areaName'],\n  data () {\n    return {\n      options: {\n        tooltip: {\n          trigger: 'item',\n          formatter: function (params) {\n            console.log('params', params)\n            const value = params.value !== undefined && params.value !== null ? params.value : 0\n            return `<div style=\"padding: 8px;\">\n              <div style=\"color: #00D4FF; margin-bottom: 4px;\">${params.name || ''}</div>\n              <div style=\"color: #FFFFFF;\">${value}</div>\n            </div>`\n          },\n          backgroundColor: 'rgba(0, 20, 40, 0.95)',\n          borderColor: '#00D4FF',\n          borderWidth: 2,\n          borderRadius: 8,\n          shadowColor: 'rgba(0, 212, 255, 0.3)',\n          shadowBlur: 10,\n          textStyle: {\n            color: '#FFFFFF',\n            fontSize: 11,\n            fontFamily: 'Microsoft YaHei, Arial, sans-serif'\n          }\n        },\n        geo: {\n          map: '智慧人大',\n          layoutCenter: ['50%', '50%'],\n          layoutSize: '90%',\n          roam: false,\n          itemStyle: {\n            borderWidth: 2,\n            borderColor: 'rgba(0, 181, 254, 1)',\n            shadowColor: 'rgba(0, 181, 254, 1)',\n            shadowBlur: 0,\n            shadowOffsetX: 0,\n            shadowOffsetY: 10\n          },\n          emphasis: {\n            borderWidth: 2,\n            borderColor: 'rgba(0, 181, 254, 1)',\n            shadowColor: 'rgba(0, 181, 254, .1)',\n            shadowBlur: 0,\n            shadowOffsetX: 0,\n            shadowOffsetY: 10,\n            areaColor: {\n              type: 'radial',\n              x: 0.5,\n              y: 0.1,\n              r: 0.9,\n              colorStops: [\n                { offset: 0, color: 'rgba(0, 181, 254, 0.6)' },\n                { offset: 1, color: 'rgba(0, 181, 254, 0.6)' }\n              ]\n            }\n          }\n        },\n        series: []\n      },\n      echartObjRef: null,\n      regionList: []\n    }\n  },\n  mounted () {\n    this.$nextTick(() => {\n      this.initMap()\n    })\n  },\n  methods: {\n    splitFileName (text) {\n      var pattern = /\\.{1}[a-z]{1,}$/\n      return text.slice(text.lastIndexOf('/') + 1, pattern.exec(text).index)\n    },\n\n    async initMap (id = this.areaId) {\n      const mapJson = await import('./qingdao.json')\n      let geoJson = mapJson.default\n      if (id && id !== '370200' && this.data) {\n        const area = this.data.find(a => a.areaId === id)\n        if (area) {\n          const feature = geoJson.features.find(f => f.properties && (f.properties.adcode === area.adcode))\n          if (feature) {\n            geoJson = { ...geoJson, features: [feature] }\n          }\n        }\n      }\n      this.renderMap(geoJson, id)\n    },\n\n    renderMap (JSONData, areaId = this.areaId) {\n      const dom = document.getElementById('homeMap')\n      if (!dom) return\n      dom.removeAttribute('_echarts_instance_')\n      const echartObj = echarts.init(dom, 'macarons')\n      this.echartObjRef = echartObj\n      echarts.registerMap('智慧人大', JSONData)\n      echartObj.setOption({\n        ...this.getOptions(areaId),\n        series: [this.getSeriesData(), this.getMapSeries(areaId)]\n      })\n      echartObj.off('click')\n      echartObj.on('click', (param) => {\n        // areaName.value = param.name\n        const area = this.data?.find(a => a.name === param.name)\n        // emit('mapClick', { name: param.name, areaId: area?.areaId, adcode: area?.adcode })\n        echartObj.setOption({\n          series: [\n            this.getSeriesData(),\n            this.getMapSeries(area?.areaId)\n          ]\n        })\n      })\n      window.addEventListener('resize', () => {\n        if (echartObj && echartObj.resize) {\n          echartObj.resize()\n        }\n      })\n    },\n\n    getOptions (areaId) {\n      this.options.geo.layoutSize = areaId === '370200' ? '90%' : '70%'\n      this.options.series = [this.getSeriesData(), this.getMapSeries(areaId)]\n      return this.options\n    },\n\n    getSeriesData () {\n      return {\n        type: 'scatter',\n        coordinateSystem: 'geo',\n        data: this.data,\n        show: true,\n        symbolSize: [20, 25],\n        geoIndex: 0,\n        symbolOffset: [0, -20]\n      }\n    },\n\n    getMapSeries () {\n      // 转换数据格式为ECharts地图系列所需的格式\n      const mapData = this.data ? this.data.map(item => ({\n        name: item.name,\n        value: Number(item.value) || 0 // 确保value是数值类型\n      })) : []\n\n      return {\n        layoutCenter: ['50%', '50%'],\n        layoutSize: '90%',\n        name: 'map',\n        type: 'map',\n        map: '智慧人大',\n        geoIndex: 2,\n        showLegendSymbol: false,\n        data: mapData,\n        label: {\n          show: true,\n          fontSize: 14,\n          position: 'center',\n          color: '#fff',\n          formatter: function (name) {\n            return name.name.length > 6 ? name.name.substring(0, 5) + '\\n' + name.name.substring(5) : name.name\n          },\n          emphasis: {\n            show: true,\n            textStyle: { color: '#fff' }\n          }\n        },\n        roam: false,\n        itemStyle: {\n          borderColor: '#00B5FE',\n          borderWidth: 2,\n          shadowColor: 'rgba(0, 181, 254, 0.6)',\n          shadowBlur: 15,\n          shadowOffsetX: 0,\n          shadowOffsetY: 15,\n          areaColor: {\n            type: 'radial',\n            x: 0.4,\n            y: 0.3,\n            r: 0.8,\n            colorStops: [\n              { offset: 0, color: 'rgba(20, 80, 150, 0.7)' },\n              { offset: 0.4, color: 'rgba(15, 60, 120, 0.8)' },\n              { offset: 0.8, color: 'rgba(10, 40, 90, 0.9)' },\n              { offset: 1, color: 'rgba(8, 25, 60, 1)' }\n            ]\n          }\n        },\n        emphasis: {\n          itemStyle: {\n            borderColor: '#00EAFF',\n            shadowColor: 'rgba(0, 234, 255, 0.8)',\n            shadowBlur: 25,\n            shadowOffsetX: 0,\n            shadowOffsetY: 20,\n            borderWidth: 3,\n            areaColor: {\n              type: 'radial',\n              x: 0.4,\n              y: 0.3,\n              r: 0.8,\n              colorStops: [\n                { offset: 0, color: 'rgba(40, 120, 200, 0.8)' },\n                { offset: 0.4, color: 'rgba(25, 90, 160, 0.9)' },\n                { offset: 0.8, color: 'rgba(15, 60, 120, 1)' },\n                { offset: 1, color: 'rgba(10, 35, 80, 1)' }\n              ]\n            }\n          }\n        }\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.map-container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n}\n\n#homeMap {\n  width: 100%;\n  height: 100%;\n}\n</style>\n"]}]}