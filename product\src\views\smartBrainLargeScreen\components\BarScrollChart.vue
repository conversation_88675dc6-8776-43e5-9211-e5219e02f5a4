<template>
  <div :id="id" class="bar-scroll-chart"></div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
  props: {
    id: {
      type: String,
      required: true
    },
    showCount: {
      type: Number,
      required: true
    },
    chartData: {
      type: Array,
      required: true,
      default: () => []
    }
  },
  data () {
    return {
      chart: null,
      timer: null,
      currentIndex: 0
      // showCount: 5 // 一屏显示5条
    }
  },
  mounted () {
    this.initChart()
    this.startScroll()
  },
  beforeDestroy () {
    if (this.chart) this.chart.dispose()
    if (this.timer) clearInterval(this.timer)
  },
  methods: {
    getBarColor () {
      return {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 1,
        y2: 0,
        colorStops: [
          { offset: 0, color: '#062553' },
          { offset: 1, color: 'rgba(31, 198, 255, 1)' }
        ]
      }
    },
    getBgBarColor () {
      return 'rgba(35,225,255,0.08)'
    },
    getOption (data) {
      return {
        grid: {
          left: this.id === 'committee-statistics' ? 15 : 35,
          right: 10,
          top: 15,
          bottom: 10,
          containLabel: true
        },
        xAxis: {
          type: 'value',
          min: 0,
          max: Math.max(...this.chartData.map(d => d.value)) * 1.1,
          splitLine: { show: false },
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: { show: false }
        },
        yAxis: [
          {
            type: 'category',
            inverse: true,
            data: data.map((item, idx) => item.name),
            axisTick: { show: false },
            axisLine: { show: false },
            axisLabel: {
              show: true,
              align: 'right',
              margin: 16,
              formatter: (value, idx) => {
                const num = ((this.currentIndex + idx) % this.chartData.length) + 1
                return `{num|${num.toString().padStart(2, '0')}}  {name|${value}}`
              },
              rich: {
                num: {
                  color: 'rgba(255, 255, 255, 0.5)',
                  fontSize: 13,
                  fontFamily: 'DIN',
                  fontWeight: '500',
                  align: 'left'
                },
                name: {
                  color: '#fff',
                  fontSize: 15,
                  padding: [0, 0, 0, 4]
                }
              }
            }
          },
          {
            // 右侧数值
            type: 'category',
            inverse: true,
            data: data.map(item => item.value),
            axisTick: { show: false },
            axisLine: { show: false },
            axisLabel: {
              show: true,
              color: '#A0F6FF',
              fontSize: 18,
              align: 'left',
              margin: 12
            }
          }
        ],
        series: [
          {
            type: 'bar',
            barWidth: 6,
            yAxisIndex: 0,
            data: data.map(item => item.value),
            itemStyle: {
              color: this.getBarColor(),
              borderRadius: 6
            },
            z: 2
          },
          {
            // 背景条
            type: 'bar',
            barWidth: 8,
            yAxisIndex: 0,
            data: data.map(() => Math.max(...this.chartData.map(d => d.value)) * 1.1),
            itemStyle: {
              color: this.getBgBarColor(),
              borderRadius: 6
            },
            barGap: '-100%',
            z: 1
          }
        ]
      }
    },
    initChart () {
      const chartContainer = document.getElementById(this.id)
      if (!chartContainer) return
      this.chart = echarts.init(chartContainer)
      this.renderChart()
      window.addEventListener('resize', this.resizeChart)
    },
    renderChart () {
      // 滚动窗口数据
      let data = []
      if (this.chartData.length <= this.showCount) {
        data = this.chartData
      } else {
        // 实现无缝滚动
        const start = this.currentIndex
        const end = start + this.showCount
        if (end <= this.chartData.length) {
          data = this.chartData.slice(start, end)
        } else {
          data = this.chartData.slice(start).concat(this.chartData.slice(0, end - this.chartData.length))
        }
      }
      // 保持原顺序，新数据在最下方
      this.chart.setOption(this.getOption(data), true)
    },
    startScroll () {
      if (this.timer) clearInterval(this.timer)
      this.timer = setInterval(() => {
        if (this.chartData.length <= this.showCount) return
        this.currentIndex = (this.currentIndex + 1) % this.chartData.length
        this.renderChart()
      }, 3000)
    },
    resizeChart () {
      if (this.chart) this.chart.resize()
    }
  }
}
</script>

<style lang="scss" scoped>
.bar-scroll-chart {
  width: 100%;
  height: 100%;
}
</style>
