{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\MapComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\MapComponent.vue", "mtime": 1755575152664}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["MapComponent.vue"], "names": [], "mappings": ";AAOA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "MapComponent.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div class=\"map-container\">\n    <div class=\"map\" id=\"homeMap\"></div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\nrequire('echarts/theme/macarons') // 引入主题\n\nexport default {\n  name: 'MapComponent',\n  props: ['data', 'areaId', 'areaName'],\n  data () {\n    return {\n      options: {\n        tooltip: {\n          trigger: 'item',\n          formatter: function (params) {\n            console.log('params', params)\n            return `<div style=\"padding: 8px;\">\n              <div style=\"color: #00D4FF; margin-bottom: 4px;\">${params.name}</div>\n              <div style=\"color: #FFFFFF;\">${params.value || 0}</div>\n            </div>`\n          },\n          backgroundColor: 'rgba(0, 20, 40, 0.95)',\n          borderColor: '#00D4FF',\n          borderWidth: 2,\n          borderRadius: 8,\n          shadowColor: 'rgba(0, 212, 255, 0.3)',\n          shadowBlur: 10,\n          textStyle: {\n            color: '#FFFFFF',\n            fontSize: 11,\n            fontFamily: 'Microsoft YaHei, Arial, sans-serif'\n          }\n        },\n        geo: {\n          map: '智慧人大',\n          layoutCenter: ['50%', '50%'],\n          layoutSize: '90%',\n          roam: false,\n          itemStyle: {\n            borderWidth: 2,\n            borderColor: 'rgba(0, 181, 254, 1)',\n            shadowColor: 'rgba(0, 181, 254, 1)',\n            shadowBlur: 0,\n            shadowOffsetX: 0,\n            shadowOffsetY: 10\n          },\n          emphasis: {\n            borderWidth: 2,\n            borderColor: 'rgba(0, 181, 254, 1)',\n            shadowColor: 'rgba(0, 181, 254, .1)',\n            shadowBlur: 0,\n            shadowOffsetX: 0,\n            shadowOffsetY: 10,\n            areaColor: {\n              type: 'radial',\n              x: 0.5,\n              y: 0.1,\n              r: 0.9,\n              colorStops: [\n                { offset: 0, color: 'rgba(0, 181, 254, 0.6)' },\n                { offset: 1, color: 'rgba(0, 181, 254, 0.6)' }\n              ]\n            }\n          }\n        },\n        series: []\n      },\n      echartObjRef: null,\n      regionList: []\n    }\n  },\n  mounted () {\n    this.$nextTick(() => {\n      this.initMap()\n    })\n  },\n  methods: {\n    splitFileName (text) {\n      var pattern = /\\.{1}[a-z]{1,}$/\n      return text.slice(text.lastIndexOf('/') + 1, pattern.exec(text).index)\n    },\n\n    async initMap (id = this.areaId) {\n      const mapJson = await import('./qingdao.json')\n      let geoJson = mapJson.default\n      if (id && id !== '370200' && this.data) {\n        const area = this.data.find(a => a.areaId === id)\n        if (area) {\n          const feature = geoJson.features.find(f => f.properties && (f.properties.adcode === area.adcode))\n          if (feature) {\n            geoJson = { ...geoJson, features: [feature] }\n          }\n        }\n      }\n      this.renderMap(geoJson, id)\n    },\n\n    renderMap (JSONData, areaId = this.areaId) {\n      const dom = document.getElementById('homeMap')\n      if (!dom) return\n      dom.removeAttribute('_echarts_instance_')\n      const echartObj = echarts.init(dom, 'macarons')\n      this.echartObjRef = echartObj\n      echarts.registerMap('智慧人大', JSONData)\n      echartObj.setOption({\n        ...this.getOptions(areaId),\n        series: [this.getSeriesData(), this.getMapSeries(areaId)]\n      })\n      echartObj.off('click')\n      echartObj.on('click', (param) => {\n        // areaName.value = param.name\n        const area = this.data?.find(a => a.name === param.name)\n        // emit('mapClick', { name: param.name, areaId: area?.areaId, adcode: area?.adcode })\n        echartObj.setOption({\n          series: [\n            this.getSeriesData(),\n            this.getMapSeries(area?.areaId)\n          ]\n        })\n      })\n      window.addEventListener('resize', () => {\n        if (echartObj && echartObj.resize) {\n          echartObj.resize()\n        }\n      })\n    },\n\n    getOptions (areaId) {\n      this.options.geo.layoutSize = areaId === '370200' ? '90%' : '70%'\n      this.options.series = [this.getSeriesData(), this.getMapSeries(areaId)]\n      return this.options\n    },\n\n    getSeriesData () {\n      return {\n        type: 'scatter',\n        coordinateSystem: 'geo',\n        data: this.data,\n        show: true,\n        symbolSize: [20, 25],\n        geoIndex: 0,\n        symbolOffset: [0, -20]\n      }\n    },\n\n    getMapSeries () {\n      return {\n        layoutCenter: ['50%', '50%'],\n        layoutSize: '90%',\n        name: 'map',\n        type: 'map',\n        map: '智慧人大',\n        geoIndex: 2,\n        showLegendSymbol: false,\n        label: {\n          show: true,\n          fontSize: 14,\n          position: 'center',\n          color: '#fff',\n          formatter: function (name) {\n            return name.name.length > 6 ? name.name.substring(0, 5) + '\\n' + name.name.substring(5) : name.name\n          },\n          emphasis: {\n            show: true,\n            textStyle: { color: '#fff' }\n          }\n        },\n        roam: false,\n        itemStyle: {\n          borderColor: '#00B5FE',\n          borderWidth: 2,\n          shadowColor: 'rgba(0, 181, 254, 0.6)',\n          shadowBlur: 15,\n          shadowOffsetX: 0,\n          shadowOffsetY: 15,\n          areaColor: {\n            type: 'radial',\n            x: 0.4,\n            y: 0.3,\n            r: 0.8,\n            colorStops: [\n              { offset: 0, color: 'rgba(20, 80, 150, 0.7)' },\n              { offset: 0.4, color: 'rgba(15, 60, 120, 0.8)' },\n              { offset: 0.8, color: 'rgba(10, 40, 90, 0.9)' },\n              { offset: 1, color: 'rgba(8, 25, 60, 1)' }\n            ]\n          }\n        },\n        emphasis: {\n          itemStyle: {\n            borderColor: '#00EAFF',\n            shadowColor: 'rgba(0, 234, 255, 0.8)',\n            shadowBlur: 25,\n            shadowOffsetX: 0,\n            shadowOffsetY: 20,\n            borderWidth: 3,\n            areaColor: {\n              type: 'radial',\n              x: 0.4,\n              y: 0.3,\n              r: 0.8,\n              colorStops: [\n                { offset: 0, color: 'rgba(40, 120, 200, 0.8)' },\n                { offset: 0.4, color: 'rgba(25, 90, 160, 0.9)' },\n                { offset: 0.8, color: 'rgba(15, 60, 120, 1)' },\n                { offset: 1, color: 'rgba(10, 35, 80, 1)' }\n              ]\n            }\n          }\n        }\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.map-container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n}\n\n#homeMap {\n  width: 100%;\n  height: 100%;\n}\n</style>\n"]}]}