{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\MapComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\MapComponent.vue", "mtime": 1755575541791}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["MapComponent.vue"], "names": [], "mappings": ";AAOA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "MapComponent.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div class=\"map-container\">\n    <div class=\"map\" id=\"homeMap\"></div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\nrequire('echarts/theme/macarons') // 引入主题\n\nexport default {\n  name: 'MapComponent',\n  props: ['data', 'areaId', 'areaName'],\n  data () {\n    return {\n      options: {\n        tooltip: {\n          trigger: 'item',\n          formatter: function (params) {\n            const value = params.value !== undefined && params.value !== null ? params.value : 0\n            return `<div style=\"padding: 8px;\">\n              <div style=\"color: #00D4FF; margin-bottom: 4px;\">${params.name || ''}</div>\n              <div style=\"color: #FFFFFF;\">${value}</div>\n            </div>`\n          },\n          backgroundColor: 'rgba(0, 20, 40, 0.95)',\n          borderColor: '#00D4FF',\n          borderWidth: 2,\n          borderRadius: 8,\n          shadowColor: 'rgba(0, 212, 255, 0.3)',\n          shadowBlur: 10,\n          textStyle: {\n            color: '#FFFFFF',\n            fontSize: 11,\n            fontFamily: 'Microsoft YaHei, Arial, sans-serif'\n          }\n        },\n        geo: {\n          map: '智慧人大',\n          layoutCenter: ['50%', '50%'],\n          layoutSize: '90%',\n          roam: false,\n          itemStyle: {\n            borderWidth: 2,\n            borderColor: 'rgba(0, 181, 254, 1)',\n            shadowColor: 'rgba(0, 181, 254, 1)',\n            shadowBlur: 0,\n            shadowOffsetX: 0,\n            shadowOffsetY: 10\n          },\n          emphasis: {\n            borderWidth: 2,\n            borderColor: 'rgba(0, 181, 254, 1)',\n            shadowColor: 'rgba(0, 181, 254, .1)',\n            shadowBlur: 0,\n            shadowOffsetX: 0,\n            shadowOffsetY: 10,\n            areaColor: {\n              type: 'radial',\n              x: 0.5,\n              y: 0.1,\n              r: 0.9,\n              colorStops: [\n                { offset: 0, color: 'rgba(0, 181, 254, 0.6)' },\n                { offset: 1, color: 'rgba(0, 181, 254, 0.6)' }\n              ]\n            }\n          }\n        },\n        series: []\n      },\n      echartObjRef: null,\n      regionList: []\n    }\n  },\n  mounted () {\n    this.$nextTick(() => {\n      this.initMap()\n    })\n  },\n  methods: {\n    splitFileName (text) {\n      var pattern = /\\.{1}[a-z]{1,}$/\n      return text.slice(text.lastIndexOf('/') + 1, pattern.exec(text).index)\n    },\n\n    async initMap (id = this.areaId) {\n      const mapJson = await import('./qingdao.json')\n      let geoJson = mapJson.default\n      if (id && id !== '370200' && this.data) {\n        const area = this.data.find(a => a.areaId === id)\n        if (area) {\n          const feature = geoJson.features.find(f => f.properties && (f.properties.adcode === area.adcode))\n          if (feature) {\n            geoJson = { ...geoJson, features: [feature] }\n          }\n        }\n      }\n      this.renderMap(geoJson, id)\n    },\n\n    renderMap (JSONData, areaId = this.areaId) {\n      const dom = document.getElementById('homeMap')\n      if (!dom) return\n      dom.removeAttribute('_echarts_instance_')\n      const echartObj = echarts.init(dom, 'macarons')\n      this.echartObjRef = echartObj\n      echarts.registerMap('智慧人大', JSONData)\n      echartObj.setOption({\n        ...this.getOptions(areaId),\n        series: [this.getSeriesData(), this.getMapSeries(areaId)]\n      })\n      echartObj.off('click')\n      echartObj.on('click', (param) => {\n        // areaName.value = param.name\n        const area = this.data?.find(a => a.name === param.name)\n        // emit('mapClick', { name: param.name, areaId: area?.areaId, adcode: area?.adcode })\n        echartObj.setOption({\n          series: [\n            this.getSeriesData(),\n            this.getMapSeries(area?.areaId)\n          ]\n        })\n      })\n      window.addEventListener('resize', () => {\n        if (echartObj && echartObj.resize) {\n          echartObj.resize()\n        }\n      })\n    },\n\n    getOptions (areaId) {\n      this.options.geo.layoutSize = areaId === '370200' ? '90%' : '70%'\n      this.options.series = [this.getSeriesData(), this.getMapSeries(areaId)]\n      return this.options\n    },\n\n    getSeriesData () {\n      return {\n        type: 'scatter',\n        coordinateSystem: 'geo',\n        data: this.data,\n        show: true,\n        symbolSize: [20, 25],\n        geoIndex: 0,\n        symbolOffset: [0, -20]\n      }\n    },\n\n    getMapSeries () {\n      // 转换数据格式为ECharts地图系列所需的格式\n      const mapData = this.data ? this.data.map(item => ({\n        name: item.name,\n        value: Number(item.value) || 0 // 确保value是数值类型\n      })) : []\n      return {\n        layoutCenter: ['50%', '50%'],\n        layoutSize: '90%',\n        name: 'map',\n        type: 'map',\n        map: '智慧人大',\n        geoIndex: 2,\n        showLegendSymbol: false,\n        data: mapData,\n        label: {\n          show: true,\n          fontSize: 14,\n          position: 'center',\n          color: '#fff',\n          formatter: function (name) {\n            return name.name.length > 6 ? name.name.substring(0, 5) + '\\n' + name.name.substring(5) : name.name\n          },\n          emphasis: {\n            show: true,\n            textStyle: { color: '#fff' }\n          }\n        },\n        roam: false,\n        itemStyle: {\n          borderColor: '#00B5FE',\n          borderWidth: 2,\n          shadowColor: 'rgba(0, 181, 254, 0.6)',\n          shadowBlur: 15,\n          shadowOffsetX: 0,\n          shadowOffsetY: 15,\n          areaColor: {\n            type: 'radial',\n            x: 0.4,\n            y: 0.3,\n            r: 0.8,\n            colorStops: [\n              { offset: 0, color: 'rgba(20, 80, 150, 0.7)' },\n              { offset: 0.4, color: 'rgba(15, 60, 120, 0.8)' },\n              { offset: 0.8, color: 'rgba(10, 40, 90, 0.9)' },\n              { offset: 1, color: 'rgba(8, 25, 60, 1)' }\n            ]\n          }\n        },\n        emphasis: {\n          itemStyle: {\n            borderColor: '#00EAFF',\n            shadowColor: 'rgba(0, 234, 255, 0.8)',\n            shadowBlur: 25,\n            shadowOffsetX: 0,\n            shadowOffsetY: 20,\n            borderWidth: 3,\n            areaColor: {\n              type: 'radial',\n              x: 0.4,\n              y: 0.3,\n              r: 0.8,\n              colorStops: [\n                { offset: 0, color: 'rgba(40, 120, 200, 0.8)' },\n                { offset: 0.4, color: 'rgba(25, 90, 160, 0.9)' },\n                { offset: 0.8, color: 'rgba(15, 60, 120, 1)' },\n                { offset: 1, color: 'rgba(10, 35, 80, 1)' }\n              ]\n            }\n          }\n        }\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.map-container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n}\n\n#homeMap {\n  width: 100%;\n  height: 100%;\n}\n</style>\n"]}]}