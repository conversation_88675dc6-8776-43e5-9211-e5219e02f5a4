<template>
  <div class="big-screen" ref="bigScreen">
    <div class="screen-header">
      <div class="header-left">
        <span class="date-time">{{ currentTime }}</span>
        <span class="weather">晴 24℃ 东南风</span>
      </div>
      <div class="header-center">
        <img src="../../../assets/largeScreen/top_header_txt.png" alt="" style="height: 50px;">
      </div>
      <div class="header-right">
        <div class="header-buttons">
          <div class="header-btn area-select-btn">
            <span>选择地区</span>
          </div>
          <div class="header-btn current-module-btn">
            <span>委员统计</span>
          </div>
          <div class="header-btn home-btn" @click="goHome">
            <span>返回首页</span>
          </div>
        </div>
      </div>
    </div>
    <div class="screen-content">
      <div class="left-panel">
        <!-- 委员数量 -->
        <div class="committee-count-section">
          <div class="header_box">
            <span class="header_text_left">委员数量</span>
          </div>
          <div class="count-content">
            <div class="count-item">
              <div class="count-value">418</div>
              <div class="count-label">委员总数</div>
            </div>
            <div class="count-item">
              <div class="count-value">42</div>
              <div class="count-label">政协常委</div>
            </div>
          </div>
        </div>

        <!-- 性别比例 -->
        <div class="gender-ratio-section">
          <div class="header_box">
            <span class="header_text_left">性别比例</span>
          </div>
          <div class="gender-content">
            <div class="gender-chart">
              <div class="ratio-item">
                <div class="ratio-circle male">70%</div>
              </div>
              <div class="ratio-item">
                <div class="ratio-circle female">30%</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 年龄 -->
        <div class="age-section">
          <div class="header_box">
            <span class="header_text_left">年龄</span>
          </div>
          <div class="age-content">
            <div class="age-pie-chart">
              <!-- 年龄饼图占位 -->
              <div class="age-chart-placeholder">年龄分布图</div>
            </div>
          </div>
        </div>

        <!-- 学历 -->
        <div class="education-section">
          <div class="header_box">
            <span class="header_text_left">学历</span>
          </div>
          <div class="education-content">
            <div class="education-bars">
              <div v-for="(item, index) in educationData" :key="index" class="education-item">
                <div class="education-label">{{ item.name }}</div>
                <div class="education-bar">
                  <div class="education-progress" :style="`width: ${item.percentage}%`"></div>
                </div>
                <div class="education-value">{{ item.value }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 党派分布 -->
        <div class="party-distribution-section">
          <div class="header_box">
            <span class="header_text_left">党派分布</span>
          </div>
          <div class="party-content">
            <div class="party-chart-container">
              <div class="party-pie-chart">
                <!-- 党派分布饼图占位 -->
                <div class="party-chart-placeholder">党派分布图</div>
              </div>
              <div class="party-legend">
                <div v-for="(item, index) in partyData" :key="index" class="party-legend-item">
                  <div class="legend-color" :style="`background-color: ${item.color}`"></div>
                  <span class="legend-label">{{ item.name }}</span>
                  <span class="legend-percentage">{{ item.percentage }}%</span>
                  <span class="legend-value">{{ item.value }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 讨论人员统计 -->
        <div class="discussion-stats-section">
          <div class="header_box">
            <span class="header_text_left">讨论组人员统计</span>
          </div>
          <div class="discussion-content">
            <div class="discussion-chart-placeholder">讨论人员统计图</div>
          </div>
        </div>
      </div>

      <div class="right-panel">
        <!-- 界别分析 -->
        <div class="sector-analysis-section">
          <div class="header_box">
            <span class="header_text_left">界别分布</span>
          </div>
          <div class="sector-content">
            <BarScrollChart id="sectorAnalysis" :showCount="30" :chart-data="sectorAnalysisData" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { useIndex } from '../screen.js'
import BarScrollChart from '../components/BarScrollChart.vue'

export default {
  name: 'BigScreen',
  components: {
    BarScrollChart
  },
  data () {
    return {
      currentTime: '',
      // 学历数据
      educationData: [
        { name: '博士', value: 101, percentage: 90 },
        { name: '硕士', value: 89, percentage: 75 },
        { name: '本科', value: 62, percentage: 60 },
        { name: '大专', value: 45, percentage: 45 },
        { name: '其他', value: 165, percentage: 100 },
        { name: '研究生', value: 84, percentage: 70 }
      ],
      // 党派数据
      partyData: [
        { name: '中国共产党', value: 32, percentage: 15, color: '#FF6B6B' },
        { name: '民革', value: 15, percentage: 8, color: '#4ECDC4' },
        { name: '民盟', value: 14, percentage: 7, color: '#45B7D1' },
        { name: '民建', value: 13, percentage: 6, color: '#96CEB4' },
        { name: '民进', value: 12, percentage: 5, color: '#FFEAA7' },
        { name: '农工党', value: 10, percentage: 4, color: '#DDA0DD' },
        { name: '致公党', value: 8, percentage: 3, color: '#98D8C8' },
        { name: '九三学社', value: 7, percentage: 3, color: '#F7DC6F' },
        { name: '台盟', value: 6, percentage: 2, color: '#BB8FCE' },
        { name: '无党派人士', value: 5, percentage: 2, color: '#85C1E9' }
      ],
      // 界别分析数据
      sectorAnalysisData: [
        { name: '经济界', value: 32 },
        { name: '教育界', value: 15 },
        { name: '科技界', value: 14 },
        { name: '工商界', value: 13 },
        { name: '医药卫生界', value: 12 },
        { name: '社会科学界', value: 10 },
        { name: '工会', value: 8 },
        { name: '共青团', value: 7 },
        { name: '妇联', value: 6 },
        { name: '科协', value: 5 },
        { name: '台联', value: 7 },
        { name: '侨联', value: 3 },
        { name: '文化艺术界', value: 24 },
        { name: '体育界', value: 16 },
        { name: '少数民族界', value: 20 },
        { name: '宗教界', value: 27 },
        { name: '特邀人士', value: 21 },
        { name: '港澳台侨', value: 5 },
        { name: '对外友好界', value: 19 },
        { name: '社会福利和社会保障界', value: 12 },
        { name: '社会治理和社会组织界', value: 21 }
      ]
    }
  },
  computed: {
  },
  mounted () {
    this.initScreen()
    this.updateTime()
    this.timeInterval = setInterval(this.updateTime, 1000)
  },
  beforeDestroy () {
    if (this.timeInterval) {
      clearInterval(this.timeInterval)
    }
  },
  methods: {
    initScreen () {
      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)
      calcRate()
      windowDraw()
    },
    updateTime () {
      const now = new Date()
      this.currentTime = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },
    // 返回首页
    goHome () {
      this.$router.push({ path: '/homeBox' })
    }
  }
}
</script>

<style lang="scss" scoped>
.big-screen {
  width: 1920px;
  height: 1080px;
  position: relative;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transform-origin: left top;
  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;
  background-size: cover;
  background-position: center;

  .screen-header {
    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;
    background-size: 100% 100%;
    background-position: center;
    height: 65px;
    display: flex;
    align-items: center;
    padding: 0 40px;

    .header-left {
      display: flex;
      gap: 20px;
      font-size: 14px;
      color: #8cc8ff;
      flex: 1;
    }

    .header-center {
      width: 60%;
      text-align: center;
    }

    .header-right {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      align-items: center;

      .header-buttons {
        display: flex;
        gap: 15px;

        .header-btn {
          padding: 8px 20px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
          color: #FFFFFF;
          background: linear-gradient(135deg, rgba(0, 181, 254, 0.8) 0%, rgba(0, 100, 200, 0.8) 100%);
          border: 1px solid rgba(0, 181, 254, 0.6);
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
          }

          &:hover {
            background: linear-gradient(135deg, rgba(0, 181, 254, 1) 0%, rgba(0, 120, 220, 1) 100%);
            border-color: rgba(0, 181, 254, 1);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 181, 254, 0.4);

            &::before {
              left: 100%;
            }
          }

          &.current-module-btn {
            background: linear-gradient(135deg, rgba(255, 214, 0, 0.9) 0%, rgba(255, 165, 0, 0.9) 100%);
            border-color: rgba(255, 214, 0, 0.8);
            color: #333;

            &:hover {
              background: linear-gradient(135deg, rgba(255, 214, 0, 1) 0%, rgba(255, 165, 0, 1) 100%);
              border-color: rgba(255, 214, 0, 1);
            }
          }

          span {
            position: relative;
            z-index: 1;
            font-weight: 500;
          }
        }
      }
    }
  }

  .screen-content {
    height: calc(100% - 65px);
    display: flex;
    padding: 20px;
    gap: 20px;

    .header_box {
      position: absolute;
      top: 15px;
      left: 24px;
      right: 0;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .header_text_left {
        font-weight: bold;
        font-size: 20px;
        color: #FFFFFF;
        cursor: pointer;
      }

      .header_text_right {
        font-size: 15px;
        color: #FFD600;
      }

      .header_text_center {
        font-size: 15px;
        color: #FFFFFF;
        display: flex;
        align-items: center;

        span {
          font-weight: 500;
          font-size: 24px;
          color: #02FBFB;
          margin: 0 10px 0 6px;
        }
      }
    }

    .left-panel {
      flex: 1;
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      grid-template-rows: 1fr 1fr 1fr;
      gap: 20px;
      height: 100%;
    }

    .right-panel {
      width: 465px;
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    // 左侧面板样式
    .left-panel {

      // 委员数量
      .committee-count-section {
        background: url('../../../assets/largeScreen/gender_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        padding: 20px;
        grid-column: 1; // 第一列
        grid-row: 1; // 第一行

        .count-content {
          display: flex;
          justify-content: space-around;
          align-items: center;
          height: 100%;
          margin-top: 30px;

          .count-item {
            text-align: center;

            .count-value {
              font-size: 48px;
              font-weight: bold;
              color: #00D4FF;
              margin-bottom: 10px;
              text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
            }

            .count-label {
              font-size: 16px;
              color: #FFFFFF;
            }
          }
        }
      }

      // 性别比例
      .gender-ratio-section {
        background: url('../../../assets/largeScreen/gender_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        padding: 20px;
        grid-column: 2; // 第二列
        grid-row: 1; // 第一行

        .gender-content {
          margin-top: 30px;
          height: 100%;

          .gender-chart {
            display: flex;
            justify-content: space-around;
            align-items: center;
            height: 100%;

            .ratio-item {
              text-align: center;

              .ratio-circle {
                width: 80px;
                height: 80px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 18px;
                font-weight: bold;
                color: #FFFFFF;
                margin-bottom: 10px;

                &.male {
                  background: conic-gradient(#00D4FF 0% 70%, rgba(0, 212, 255, 0.2) 70% 100%);
                }

                &.female {
                  background: conic-gradient(#FFD600 0% 30%, rgba(255, 214, 0, 0.2) 30% 100%);
                }
              }
            }
          }
        }
      }

      // 年龄
      .age-section {
        background: url('../../../assets/largeScreen/age_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        padding: 20px;
        grid-column: 3; // 第三列
        grid-row: 1; // 第一行

        .age-content {
          margin-top: 30px;
          height: calc(100% - 50px);

          .age-pie-chart {
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;

            .age-chart-placeholder {
              color: #FFFFFF;
              font-size: 16px;
              text-align: center;
            }
          }
        }
      }

      // 学历
      .education-section {
        background: url('../../../assets/largeScreen/education_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        padding: 20px;
        grid-column: 1; // 第一列
        grid-row: 2; // 第二行

        .education-content {
          margin-top: 30px;
          height: calc(100% - 50px);

          .education-bars {
            height: 100%;
            overflow-y: auto;

            .education-item {
              display: flex;
              align-items: center;
              margin-bottom: 15px;
              gap: 10px;

              .education-label {
                width: 60px;
                font-size: 14px;
                color: #FFFFFF;
                flex-shrink: 0;
              }

              .education-bar {
                flex: 1;
                height: 8px;
                background: rgba(0, 181, 254, 0.2);
                border-radius: 4px;
                overflow: hidden;

                .education-progress {
                  height: 100%;
                  background: linear-gradient(90deg, #00D4FF 0%, #0080FF 100%);
                  border-radius: 4px;
                  transition: width 0.3s ease;
                }
              }

              .education-value {
                width: 30px;
                font-size: 14px;
                color: #00D4FF;
                text-align: right;
                flex-shrink: 0;
              }
            }
          }
        }
      }

      // 党派分布
      .party-distribution-section {
        background: url('../../../assets/largeScreen/party_distribution_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        padding: 20px;
        grid-column: 2 / 4; // 跨越第2和第3列（在第二行）
        grid-row: 2; // 明确指定在第二行

        .party-content {
          margin-top: 30px;
          height: calc(100% - 50px);

          .party-chart-container {
            display: flex;
            height: 100%;
            gap: 20px;

            .party-pie-chart {
              width: 200px;
              display: flex;
              align-items: center;
              justify-content: center;

              .party-chart-placeholder {
                color: #FFFFFF;
                font-size: 16px;
                text-align: center;
              }
            }

            .party-legend {
              flex: 1;
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 8px;
              max-height: 100%;
              overflow-y: auto;

              .party-legend-item {
                display: flex;
                align-items: center;
                gap: 8px;
                font-size: 12px;

                .legend-color {
                  width: 12px;
                  height: 12px;
                  border-radius: 2px;
                  flex-shrink: 0;
                }

                .legend-label {
                  color: #FFFFFF;
                  flex: 1;
                  min-width: 0;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }

                .legend-percentage {
                  color: #00D4FF;
                  font-weight: 500;
                  width: 30px;
                  text-align: right;
                }

                .legend-value {
                  color: #FFD600;
                  font-weight: 500;
                  width: 25px;
                  text-align: right;
                }
              }
            }
          }
        }
      }

      // 讨论人员统计
      .discussion-stats-section {
        background: url('../../../assets/largeScreen/discussion_stats_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        padding: 20px;
        grid-column: 1 / -1; // 跨越三列（讨论组人员统计在第三行）
        grid-row: 3; // 明确指定在第三行

        .discussion-content {
          margin-top: 30px;
          height: calc(100% - 50px);
          display: flex;
          align-items: center;
          justify-content: center;

          .discussion-chart-placeholder {
            color: #FFFFFF;
            font-size: 16px;
            text-align: center;
          }
        }
      }
    }

    .right-panel {

      // 界别分析
      .sector-analysis-section {
        background: url('../../../assets/largeScreen/sector_analysis_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        padding: 20px;
        height: 100%;

        .sector-content {
          margin-top: 30px;
          height: calc(100% - 25px);
        }
      }
    }
  }
}
</style>
