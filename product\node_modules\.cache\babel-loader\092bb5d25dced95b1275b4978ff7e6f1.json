{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\BarScrollChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\BarScrollChart.vue", "mtime": 1755589243987}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAKA;AAEA;EACAA,sBADA;EAEAC;IACAC;MACAC,YADA;MAEAC;IAFA,CADA;IAKAC;MACAF,YADA;MAEAC;IAFA,CALA;IASAE;MACAH,WADA;MAEAC,cAFA;MAGAG;IAHA;EATA,CAFA;;EAiBAC;IACA;MACAC,WADA;MAEAC,WAFA;MAGAC,eAHA,CAIA;;IAJA;EAMA,CAxBA;;EAyBAC;IACA;IACA;EACA,CA5BA;;EA6BAC;IACA;IACA;EACA,CAhCA;;EAiCAC;IACAC;MACA;QACAZ,cADA;QAEAa,IAFA;QAGAC,IAHA;QAIAC,KAJA;QAKAC,KALA;QAMAC,aACA;UAAAC;UAAAC;QAAA,CADA,EAEA;UAAAD;UAAAC;QAAA,CAFA;MANA;IAWA,CAbA;;IAcAC;MACA;IACA,CAhBA;;IAiBAC;MACA;QACAC;UACAC,kDADA;UAEAC,SAFA;UAGAC,OAHA;UAIAC,UAJA;UAKAC;QALA,CADA;QAQAC;UACA5B,aADA;UAEA6B,MAFA;UAGAC,wDAHA;UAIAC;YAAAC;UAAA,CAJA;UAKAC;YAAAD;UAAA,CALA;UAMAE;YAAAF;UAAA,CANA;UAOAG;YAAAH;UAAA;QAPA,CARA;QAiBAI,QACA;UACA;UACApC,gBAFA;UAGAqC,aAHA;UAIAhC;YACA;YACA;UACA,CAHA,CAJA;UAQA6B;YAAAF;UAAA,CARA;UASAC;YAAAD;UAAA,CATA;UAUAG;YACAH,UADA;YAEAb,iCAFA;YAGAmB,YAHA;YAIAC,iBAJA;YAKAC,iBALA;YAMAC,eANA;YAOAC;UAPA,CAVA;UAmBAC,gBAnBA;UAoBAzB;QApBA,CADA,EAuBA;UACA;UACAlB,gBAFA;UAGAqC,aAHA;UAIAhC,iCAJA;UAKA6B;YAAAF;UAAA,CALA;UAMAC;YAAAD;UAAA,CANA;UAOAG;YACAH,UADA;YAEAb,aAFA;YAGAmB,YAHA;YAIAG,aAJA;YAKAC;UALA;QAPA,CAvBA,EAsCA;UACA;UACA1C,gBAFA;UAGAqC,aAHA;UAIAhC,kCAJA;UAKA6B;YAAAF;UAAA,CALA;UAMAC;YAAAD;UAAA,CANA;UAOAG;YACAH,UADA;YAEAb,gBAFA;YAGAmB,YAHA;YAIAG,aAJA;YAKAC;UALA;QAPA,CAtCA,CAjBA;QAuEAE,SACA;UACA5C,WADA;UAEA6C,WAFA;UAGAC,aAHA;UAGA;UACAzC,kCAJA;UAKA0C;YACA5B,yBADA;YAEA6B;UAFA,CALA;UASAC;QATA,CADA,EAYA;UACA;UACAjD,WAFA;UAGA6C,WAHA;UAIAC,aAJA;UAIA;UACAzC,yEALA;UAMA0C;YACA5B,2BADA;YAEA6B;UAFA,CANA;UAUAE,eAVA;UAWAD;QAXA,CAZA;MAvEA;IAkGA,CApHA;;IAqHAE;MACA;MACA;MACA;MACA;MACAC;IACA,CA3HA;;IA4HAC;MACA;MACA;;MACA;QACAhD;MACA,CAFA,MAEA;QACA;QACA;QACA;;QACA;UACAA;QACA,CAFA,MAEA;UACAA;QACA;MACA,CAdA,CAeA;;;MACA;IACA,CA7IA;;IA8IAiD;MACA;MACA;QACA;QACA;QACA;MACA,CAJA,EAIA,IAJA;IAKA,CArJA;;IAsJAC;MACA;IACA;;EAxJA;AAjCA", "names": ["name", "props", "id", "type", "required", "showCount", "chartData", "default", "data", "chart", "timer", "currentIndex", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "getBarColor", "x", "y", "x2", "y2", "colorStops", "offset", "color", "getBgBarColor", "getOption", "grid", "left", "right", "top", "bottom", "containLabel", "xAxis", "min", "max", "splitLine", "show", "axisLine", "axisTick", "axisLabel", "yAxis", "inverse", "fontSize", "fontFamily", "fontWeight", "align", "margin", "position", "series", "<PERSON><PERSON><PERSON><PERSON>", "yAxisIndex", "itemStyle", "borderRadius", "z", "barGap", "initChart", "window", "<PERSON><PERSON><PERSON>", "startScroll", "resizeChart"], "sourceRoot": "src/views/smartBrainLargeScreen/components", "sources": ["BarScrollChart.vue"], "sourcesContent": ["<template>\r\n  <div :id=\"id\" class=\"bar-scroll-chart\"></div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\n\r\nexport default {\r\n  name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\r\n  props: {\r\n    id: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    showCount: {\r\n      type: Number,\r\n      required: true\r\n    },\r\n    chartData: {\r\n      type: Array,\r\n      required: true,\r\n      default: () => []\r\n    }\r\n  },\r\n  data () {\r\n    return {\r\n      chart: null,\r\n      timer: null,\r\n      currentIndex: 0\r\n      // showCount: 5 // 一屏显示5条\r\n    }\r\n  },\r\n  mounted () {\r\n    this.initChart()\r\n    this.startScroll()\r\n  },\r\n  beforeDestroy () {\r\n    if (this.chart) this.chart.dispose()\r\n    if (this.timer) clearInterval(this.timer)\r\n  },\r\n  methods: {\r\n    getBarColor () {\r\n      return {\r\n        type: 'linear',\r\n        x: 0,\r\n        y: 0,\r\n        x2: 1,\r\n        y2: 0,\r\n        colorStops: [\r\n          { offset: 0, color: '#062553' },\r\n          { offset: 1, color: 'rgba(31, 198, 255, 1)' }\r\n        ]\r\n      }\r\n    },\r\n    getBgBarColor () {\r\n      return 'rgba(35,225,255,0.08)'\r\n    },\r\n    getOption (data) {\r\n      return {\r\n        grid: {\r\n          left: this.id === 'committee-statistics' ? 50 : 70,\r\n          right: 10,\r\n          top: 15,\r\n          bottom: 10,\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'value',\r\n          min: 0,\r\n          max: Math.max(...this.chartData.map(d => d.value)) * 1.1,\r\n          splitLine: { show: false },\r\n          axisLine: { show: false },\r\n          axisTick: { show: false },\r\n          axisLabel: { show: false }\r\n        },\r\n        yAxis: [\r\n          {\r\n            // 序号轴 - 固定在最左侧\r\n            type: 'category',\r\n            inverse: true,\r\n            data: data.map((_, idx) => {\r\n              const num = ((this.currentIndex + idx) % this.chartData.length) + 1\r\n              return num.toString().padStart(2, '0')\r\n            }),\r\n            axisTick: { show: false },\r\n            axisLine: { show: false },\r\n            axisLabel: {\r\n              show: true,\r\n              color: 'rgba(255, 255, 255, 0.5)',\r\n              fontSize: 13,\r\n              fontFamily: 'DIN',\r\n              fontWeight: '500',\r\n              align: 'center',\r\n              margin: 8\r\n            },\r\n            position: 'left',\r\n            offset: -50\r\n          },\r\n          {\r\n            // 名称轴\r\n            type: 'category',\r\n            inverse: true,\r\n            data: data.map(item => item.name),\r\n            axisTick: { show: false },\r\n            axisLine: { show: false },\r\n            axisLabel: {\r\n              show: true,\r\n              color: '#fff',\r\n              fontSize: 15,\r\n              align: 'left',\r\n              margin: 16\r\n            }\r\n          },\r\n          {\r\n            // 右侧数值\r\n            type: 'category',\r\n            inverse: true,\r\n            data: data.map(item => item.value),\r\n            axisTick: { show: false },\r\n            axisLine: { show: false },\r\n            axisLabel: {\r\n              show: true,\r\n              color: '#A0F6FF',\r\n              fontSize: 18,\r\n              align: 'left',\r\n              margin: 12\r\n            }\r\n          }\r\n        ],\r\n        series: [\r\n          {\r\n            type: 'bar',\r\n            barWidth: 6,\r\n            yAxisIndex: 1, // 使用名称轴\r\n            data: data.map(item => item.value),\r\n            itemStyle: {\r\n              color: this.getBarColor(),\r\n              borderRadius: 6\r\n            },\r\n            z: 2\r\n          },\r\n          {\r\n            // 背景条\r\n            type: 'bar',\r\n            barWidth: 8,\r\n            yAxisIndex: 1, // 使用名称轴\r\n            data: data.map(() => Math.max(...this.chartData.map(d => d.value)) * 1.1),\r\n            itemStyle: {\r\n              color: this.getBgBarColor(),\r\n              borderRadius: 6\r\n            },\r\n            barGap: '-100%',\r\n            z: 1\r\n          }\r\n        ]\r\n      }\r\n    },\r\n    initChart () {\r\n      const chartContainer = document.getElementById(this.id)\r\n      if (!chartContainer) return\r\n      this.chart = echarts.init(chartContainer)\r\n      this.renderChart()\r\n      window.addEventListener('resize', this.resizeChart)\r\n    },\r\n    renderChart () {\r\n      // 滚动窗口数据\r\n      let data = []\r\n      if (this.chartData.length <= this.showCount) {\r\n        data = this.chartData\r\n      } else {\r\n        // 实现无缝滚动\r\n        const start = this.currentIndex\r\n        const end = start + this.showCount\r\n        if (end <= this.chartData.length) {\r\n          data = this.chartData.slice(start, end)\r\n        } else {\r\n          data = this.chartData.slice(start).concat(this.chartData.slice(0, end - this.chartData.length))\r\n        }\r\n      }\r\n      // 保持原顺序，新数据在最下方\r\n      this.chart.setOption(this.getOption(data), true)\r\n    },\r\n    startScroll () {\r\n      if (this.timer) clearInterval(this.timer)\r\n      this.timer = setInterval(() => {\r\n        if (this.chartData.length <= this.showCount) return\r\n        this.currentIndex = (this.currentIndex + 1) % this.chartData.length\r\n        this.renderChart()\r\n      }, 3000)\r\n    },\r\n    resizeChart () {\r\n      if (this.chart) this.chart.resize()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.bar-scroll-chart {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n</style>\r\n"]}]}