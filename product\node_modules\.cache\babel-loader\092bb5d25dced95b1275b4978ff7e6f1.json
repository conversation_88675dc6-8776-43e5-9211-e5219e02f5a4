{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\BarScrollChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\BarScrollChart.vue", "mtime": 1755588948948}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAKA;AAEA;EACAA,sBADA;EAEAC;IACAC;MACAC,YADA;MAEAC;IAFA,CADA;IAKAC;MACAF,YADA;MAEAC;IAFA,CALA;IASAE;MACAH,WADA;MAEAC,cAFA;MAGAG;IAHA;EATA,CAFA;;EAiBAC;IACA;MACAC,WADA;MAEAC,WAFA;MAGAC,eAHA,CAIA;;IAJA;EAMA,CAxBA;;EAyBAC;IACA;IACA;EACA,CA5BA;;EA6BAC;IACA;IACA;EACA,CAhCA;;EAiCAC;IACAC;MACA;QACAZ,cADA;QAEAa,IAFA;QAGAC,IAHA;QAIAC,KAJA;QAKAC,KALA;QAMAC,aACA;UAAAC;UAAAC;QAAA,CADA,EAEA;UAAAD;UAAAC;QAAA,CAFA;MANA;IAWA,CAbA;;IAcAC;MACA;IACA,CAhBA;;IAiBAC;MACA;QACAC;UACAC,kDADA;UAEAC,SAFA;UAGAC,OAHA;UAIAC,UAJA;UAKAC;QALA,CADA;QAQAC;UACA5B,aADA;UAEA6B,MAFA;UAGAC,wDAHA;UAIAC;YAAAC;UAAA,CAJA;UAKAC;YAAAD;UAAA,CALA;UAMAE;YAAAF;UAAA,CANA;UAOAG;YAAAH;UAAA;QAPA,CARA;QAiBAI,QACA;UACApC,gBADA;UAEAqC,aAFA;UAGAhC,wCAHA;UAIA6B;YAAAF;UAAA,CAJA;UAKAC;YAAAD;UAAA,CALA;UAMAG;YACAH,UADA;YAEAM,cAFA;YAGAC,UAHA;YAIAC;cACA;cACA;YACA,CAPA;YAQAC;cACAC;gBACAvB,iCADA;gBAEAwB,YAFA;gBAGAC,iBAHA;gBAIAC,iBAJA;gBAKAP;cALA,CADA;cAQAzC;gBACAsB,aADA;gBAEAwB,YAFA;gBAGAG;cAHA;YARA;UARA;QANA,CADA,EA+BA;UACA;UACA9C,gBAFA;UAGAqC,aAHA;UAIAhC,kCAJA;UAKA6B;YAAAF;UAAA,CALA;UAMAC;YAAAD;UAAA,CANA;UAOAG;YACAH,UADA;YAEAb,gBAFA;YAGAwB,YAHA;YAIAL,aAJA;YAKAC;UALA;QAPA,CA/BA,CAjBA;QAgEAQ,SACA;UACA/C,WADA;UAEAgD,WAFA;UAGAC,aAHA;UAIA5C,kCAJA;UAKA6C;YACA/B,yBADA;YAEAgC;UAFA,CALA;UASAC;QATA,CADA,EAYA;UACA;UACApD,WAFA;UAGAgD,WAHA;UAIAC,aAJA;UAKA5C,yEALA;UAMA6C;YACA/B,2BADA;YAEAgC;UAFA,CANA;UAUAE,eAVA;UAWAD;QAXA,CAZA;MAhEA;IA2FA,CA7GA;;IA8GAE;MACA;MACA;MACA;MACA;MACAC;IACA,CApHA;;IAqHAC;MACA;MACA;;MACA;QACAnD;MACA,CAFA,MAEA;QACA;QACA;QACA;;QACA;UACAA;QACA,CAFA,MAEA;UACAA;QACA;MACA,CAdA,CAeA;;;MACA;IACA,CAtIA;;IAuIAoD;MACA;MACA;QACA;QACA;QACA;MACA,CAJA,EAIA,IAJA;IAKA,CA9IA;;IA+IAC;MACA;IACA;;EAjJA;AAjCA", "names": ["name", "props", "id", "type", "required", "showCount", "chartData", "default", "data", "chart", "timer", "currentIndex", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "getBarColor", "x", "y", "x2", "y2", "colorStops", "offset", "color", "getBgBarColor", "getOption", "grid", "left", "right", "top", "bottom", "containLabel", "xAxis", "min", "max", "splitLine", "show", "axisLine", "axisTick", "axisLabel", "yAxis", "inverse", "align", "margin", "formatter", "rich", "num", "fontSize", "fontFamily", "fontWeight", "padding", "series", "<PERSON><PERSON><PERSON><PERSON>", "yAxisIndex", "itemStyle", "borderRadius", "z", "barGap", "initChart", "window", "<PERSON><PERSON><PERSON>", "startScroll", "resizeChart"], "sourceRoot": "src/views/smartBrainLargeScreen/components", "sources": ["BarScrollChart.vue"], "sourcesContent": ["<template>\r\n  <div :id=\"id\" class=\"bar-scroll-chart\"></div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\n\r\nexport default {\r\n  name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\r\n  props: {\r\n    id: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    showCount: {\r\n      type: Number,\r\n      required: true\r\n    },\r\n    chartData: {\r\n      type: Array,\r\n      required: true,\r\n      default: () => []\r\n    }\r\n  },\r\n  data () {\r\n    return {\r\n      chart: null,\r\n      timer: null,\r\n      currentIndex: 0\r\n      // showCount: 5 // 一屏显示5条\r\n    }\r\n  },\r\n  mounted () {\r\n    this.initChart()\r\n    this.startScroll()\r\n  },\r\n  beforeDestroy () {\r\n    if (this.chart) this.chart.dispose()\r\n    if (this.timer) clearInterval(this.timer)\r\n  },\r\n  methods: {\r\n    getBarColor () {\r\n      return {\r\n        type: 'linear',\r\n        x: 0,\r\n        y: 0,\r\n        x2: 1,\r\n        y2: 0,\r\n        colorStops: [\r\n          { offset: 0, color: '#062553' },\r\n          { offset: 1, color: 'rgba(31, 198, 255, 1)' }\r\n        ]\r\n      }\r\n    },\r\n    getBgBarColor () {\r\n      return 'rgba(35,225,255,0.08)'\r\n    },\r\n    getOption (data) {\r\n      return {\r\n        grid: {\r\n          left: this.id === 'committee-statistics' ? 15 : 35,\r\n          right: 10,\r\n          top: 15,\r\n          bottom: 10,\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'value',\r\n          min: 0,\r\n          max: Math.max(...this.chartData.map(d => d.value)) * 1.1,\r\n          splitLine: { show: false },\r\n          axisLine: { show: false },\r\n          axisTick: { show: false },\r\n          axisLabel: { show: false }\r\n        },\r\n        yAxis: [\r\n          {\r\n            type: 'category',\r\n            inverse: true,\r\n            data: data.map((item, idx) => item.name),\r\n            axisTick: { show: false },\r\n            axisLine: { show: false },\r\n            axisLabel: {\r\n              show: true,\r\n              align: 'right',\r\n              margin: 16,\r\n              formatter: (value, idx) => {\r\n                const num = ((this.currentIndex + idx) % this.chartData.length) + 1\r\n                return `{num|${num.toString().padStart(2, '0')}}  {name|${value}}`\r\n              },\r\n              rich: {\r\n                num: {\r\n                  color: 'rgba(255, 255, 255, 0.5)',\r\n                  fontSize: 13,\r\n                  fontFamily: 'DIN',\r\n                  fontWeight: '500',\r\n                  align: 'left'\r\n                },\r\n                name: {\r\n                  color: '#fff',\r\n                  fontSize: 15,\r\n                  padding: [0, 0, 0, 4]\r\n                }\r\n              }\r\n            }\r\n          },\r\n          {\r\n            // 右侧数值\r\n            type: 'category',\r\n            inverse: true,\r\n            data: data.map(item => item.value),\r\n            axisTick: { show: false },\r\n            axisLine: { show: false },\r\n            axisLabel: {\r\n              show: true,\r\n              color: '#A0F6FF',\r\n              fontSize: 18,\r\n              align: 'left',\r\n              margin: 12\r\n            }\r\n          }\r\n        ],\r\n        series: [\r\n          {\r\n            type: 'bar',\r\n            barWidth: 6,\r\n            yAxisIndex: 0,\r\n            data: data.map(item => item.value),\r\n            itemStyle: {\r\n              color: this.getBarColor(),\r\n              borderRadius: 6\r\n            },\r\n            z: 2\r\n          },\r\n          {\r\n            // 背景条\r\n            type: 'bar',\r\n            barWidth: 8,\r\n            yAxisIndex: 0,\r\n            data: data.map(() => Math.max(...this.chartData.map(d => d.value)) * 1.1),\r\n            itemStyle: {\r\n              color: this.getBgBarColor(),\r\n              borderRadius: 6\r\n            },\r\n            barGap: '-100%',\r\n            z: 1\r\n          }\r\n        ]\r\n      }\r\n    },\r\n    initChart () {\r\n      const chartContainer = document.getElementById(this.id)\r\n      if (!chartContainer) return\r\n      this.chart = echarts.init(chartContainer)\r\n      this.renderChart()\r\n      window.addEventListener('resize', this.resizeChart)\r\n    },\r\n    renderChart () {\r\n      // 滚动窗口数据\r\n      let data = []\r\n      if (this.chartData.length <= this.showCount) {\r\n        data = this.chartData\r\n      } else {\r\n        // 实现无缝滚动\r\n        const start = this.currentIndex\r\n        const end = start + this.showCount\r\n        if (end <= this.chartData.length) {\r\n          data = this.chartData.slice(start, end)\r\n        } else {\r\n          data = this.chartData.slice(start).concat(this.chartData.slice(0, end - this.chartData.length))\r\n        }\r\n      }\r\n      // 保持原顺序，新数据在最下方\r\n      this.chart.setOption(this.getOption(data), true)\r\n    },\r\n    startScroll () {\r\n      if (this.timer) clearInterval(this.timer)\r\n      this.timer = setInterval(() => {\r\n        if (this.chartData.length <= this.showCount) return\r\n        this.currentIndex = (this.currentIndex + 1) % this.chartData.length\r\n        this.renderChart()\r\n      }, 3000)\r\n    },\r\n    resizeChart () {\r\n      if (this.chart) this.chart.resize()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.bar-scroll-chart {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n</style>\r\n"]}]}