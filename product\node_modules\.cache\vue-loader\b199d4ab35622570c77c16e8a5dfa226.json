{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\committeeStatistics\\committeeStatisticsBox.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\committeeStatistics\\committeeStatisticsBox.vue", "mtime": 1755586518266}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgeyB1c2VJbmRleCB9IGZyb20gJy4uL3NjcmVlbi5qcycNCmltcG9ydCBNYXBDb21wb25lbnQgZnJvbSAnLi4vY29tcG9uZW50cy9NYXBDb21wb25lbnQudnVlJw0KaW1wb3J0IFBpZUNoYXJ0IGZyb20gJy4uL2NvbXBvbmVudHMvUGllQ2hhcnQudnVlJw0KaW1wb3J0IEJhclNjcm9sbENoYXJ0IGZyb20gJy4uL2NvbXBvbmVudHMvQmFyU2Nyb2xsQ2hhcnQudnVlJw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdCaWdTY3JlZW4nLA0KICBjb21wb25lbnRzOiB7DQogICAgTWFwQ29tcG9uZW50LA0KICAgIFBpZUNoYXJ0LA0KICAgIEJhclNjcm9sbENoYXJ0DQogIH0sDQogIGRhdGEgKCkgew0KICAgIHJldHVybiB7DQogICAgICBjdXJyZW50VGltZTogJycsDQogICAgICAvLyDlp5TlkZjnu5/orqENCiAgICAgIGNvbW1pdHRlZVN0YXRpc3RpY3NOdW06IFsNCiAgICAgICAgeyBpY29uOiByZXF1aXJlKCcuLi8uLi8uLi9hc3NldHMvbGFyZ2VTY3JlZW4vaWNvbl9jcHBjY19tZW1iZXIucG5nJyksIGxhYmVsOiAn5pS/5Y2P5aeU5ZGY77yI5Lq677yJJywgdmFsdWU6ICcxMDA5NScsIGNvbG9yOiAnI2ZmZmZmZicgfSwNCiAgICAgICAgeyBpY29uOiByZXF1aXJlKCcuLi8uLi8uLi9hc3NldHMvbGFyZ2VTY3JlZW4vaWNvbl9jb21taXR0ZWVfbWVtYmVyLnBuZycpLCBsYWJlbDogJ+aUv+WNj+W4uOWnlCcsIHZhbHVlOiAnODc0MicsIGNvbG9yOiAnI0ZDRDYwMycgfQ0KICAgICAgXSwNCiAgICAgIC8vIOWnlOWRmOe7n+iuoeafseeKtuWbvuaVsOaNrg0KICAgICAgY29tbWl0dGVlQmFyRGF0YTogWw0KICAgICAgICB7IG5hbWU6ICfkuK3lhbEnLCB2YWx1ZTogMzIgfSwNCiAgICAgICAgeyBuYW1lOiAn5rCR6Z2pJywgdmFsdWU6IDE1IH0sDQogICAgICAgIHsgbmFtZTogJ+awkeebnycsIHZhbHVlOiAxNCB9LA0KICAgICAgICB7IG5hbWU6ICfmsJHlu7onLCB2YWx1ZTogMTMgfSwNCiAgICAgICAgeyBuYW1lOiAn5rCR6L+bJywgdmFsdWU6IDEyIH0sDQogICAgICAgIHsgbmFtZTogJ+WGnOW3pScsIHZhbHVlOiAxMCB9LA0KICAgICAgICB7IG5hbWU6ICfoh7TlhawnLCB2YWx1ZTogOCB9LA0KICAgICAgICB7IG5hbWU6ICfkuZ3kuIknLCB2YWx1ZTogNyB9LA0KICAgICAgICB7IG5hbWU6ICflj7Dnm58nLCB2YWx1ZTogNiB9LA0KICAgICAgICB7IG5hbWU6ICfml6DlhZrmtL4nLCB2YWx1ZTogNSB9DQogICAgICBdLA0KICAgICAgLy8g5o+Q5qGI57uf6K6hDQogICAgICBwcm9wb3NhbFN0YXRpc3RpY3NOdW06IFsNCiAgICAgICAgeyBpY29uOiByZXF1aXJlKCcuLi8uLi8uLi9hc3NldHMvbGFyZ2VTY3JlZW4vaWNvbl9jb21taXR0ZWVfcHJvcG9zYWwucG5nJyksIGxhYmVsOiAn5aeU5ZGY5o+Q5qGIJywgdmFsdWU6ICc0NTYnLCB1bml0OiAn5Lu2JywgY29sb3I6ICcjMERCQ0RCJyB9LA0KICAgICAgICB7IGljb246IHJlcXVpcmUoJy4uLy4uLy4uL2Fzc2V0cy9sYXJnZVNjcmVlbi9pY29uX2NpcmNsZXNfcHJvcG9zYWwucG5nJyksIGxhYmVsOiAn55WM5Yir5o+Q5qGIJywgdmFsdWU6ICczNTQnLCB1bml0OiAn5Lu2JywgY29sb3I6ICcjMDA1OEZGJyB9LA0KICAgICAgICB7IGljb246IHJlcXVpcmUoJy4uLy4uLy4uL2Fzc2V0cy9sYXJnZVNjcmVlbi9pY29uX2NvbW1pdHRlZV9wcm9wb3NhbC5wbmcnKSwgbGFiZWw6ICfnu4Tnu4fmj5DmoYgnLCB2YWx1ZTogJzIxMScsIHVuaXQ6ICfku7YnLCBjb2xvcjogJyMxQUVCREQnIH0NCiAgICAgIF0sDQogICAgICAvLyDmj5DmoYjnu5/orqHlm77ooajmlbDmja4NCiAgICAgIHByb3Bvc2FsQ2hhcnREYXRhOiBbDQogICAgICAgIHsgbmFtZTogJ+aUv+W6nOWItue6picsIHZhbHVlOiAyMi41MiB9LA0KICAgICAgICB7IG5hbWU6ICfljr/ljLrluILmlL8nLCB2YWx1ZTogMTguMzMgfSwNCiAgICAgICAgeyBuYW1lOiAn5Y+45rOV5rOV5rK7JywgdmFsdWU6IDE1LjczIH0sDQogICAgICAgIHsgbmFtZTogJ+WMuuW4guaUv+W6nCcsIHZhbHVlOiAxMS4zNCB9LA0KICAgICAgICB7IG5hbWU6ICfnp5HmioDlt6XllYYnLCB2YWx1ZTogOS41NiB9LA0KICAgICAgICB7IG5hbWU6ICfmlZnogrLmlofljJYnLCB2YWx1ZTogOC4wOSB9LA0KICAgICAgICB7IG5hbWU6ICfmtL7lh7rmnLrmnoQnLCB2YWx1ZTogNC4yMSB9LA0KICAgICAgICB7IG5hbWU6ICfnpL7kvJrkuovkuJonLCB2YWx1ZTogMy43MSB9LA0KICAgICAgICB7IG5hbWU6ICfkvIHkuovkuJonLCB2YWx1ZTogMy42NSB9LA0KICAgICAgICB7IG5hbWU6ICflhpzmnZHljavnlJ8nLCB2YWx1ZTogMy4yMSB9LA0KICAgICAgICB7IG5hbWU6ICflhbbku5bmnLrmnoQnLCB2YWx1ZTogMS44NiB9LA0KICAgICAgICB7IG5hbWU6ICflkITnvqTkvZPku5YnLCB2YWx1ZTogMS4wMiB9DQogICAgICBdLA0KICAgICAgcHJvcG9zYWxDaGFydE5hbWU6ICfmj5DmoYjnu5/orqEnLA0KICAgICAgLy8g5bel5L2c5Yqo5oCB5pWw5o2uDQogICAgICB3b3JrRHluYW1pY3NEYXRhOiBbDQogICAgICAgIHsNCiAgICAgICAgICBpZDogMSwNCiAgICAgICAgICB0aXRsZTogJ+W4guaUv+WNj+ekvuS8muWSjOazleWItuW3peS9nOWKnuWFrOWupOWbtOe7lSLlsYXlrrbpgILogIHljJbmlLnpgKAi5byA5bGV5LiT6aKY6LCD56CUJywNCiAgICAgICAgICBkYXRlOiAnMjAyNS0wNi0wMycNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiAyLA0KICAgICAgICAgIHRpdGxlOiAnIuS4juawkeWQjOihjCDlhbHliJvlhbHotaIi5paw5qC85bGA5LiL5rCR6JCl5LyB5Lia6L2s5Z6L5Y+R5bGV5bqn6LCI5Lya5Y+s5byAJywNCiAgICAgICAgICBkYXRlOiAnMjAyNS0wNS0zMCcNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiAzLA0KICAgICAgICAgIHRpdGxlOiAnIuaDoOawkeeUn8K35Z+65bGC6KGMIuS5ieiviua0u+WKqOa4qeaaluS6uuW/gycsDQogICAgICAgICAgZGF0ZTogJzIwMjUtMDUtMzAnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpZDogNCwNCiAgICAgICAgICB0aXRsZTogJ+W4guenkeaKgOWxgOmdouWkjeW4guaUv+WNj+enkeaKgOeVjOWIq+aPkOahiCcsDQogICAgICAgICAgZGF0ZTogJzIwMjUtMDUtMzAnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpZDogNSwNCiAgICAgICAgICB0aXRsZTogJ+W4guaUv+WNj+WPrOW8gCLmjqjov5vmlbDlrZfljJbovazlnosi5LiT6aKY5Y2P5ZWG5LyaJywNCiAgICAgICAgICBkYXRlOiAnMjAyNS0wNS0yOCcNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiA2LA0KICAgICAgICAgIHRpdGxlOiAn5pS/5Y2P5aeU5ZGY5rex5YWl5Z+65bGC5byA5bGVIuS4ieacjeWKoSLmtLvliqgnLA0KICAgICAgICAgIGRhdGU6ICcyMDI1LTA1LTI1Jw0KICAgICAgICB9DQogICAgICBdLA0KICAgICAgLy8g5bGl6IGM57uf6K6h5pWw5o2uDQogICAgICBwZXJmb3JtYW5jZURhdGE6IFsNCiAgICAgICAgeyBuYW1lOiAn6ams5bmz5a6JJywgbWVldGluZzogNTE1LCBwcm9wb3NhbDogMTUsIG9waW5pb246IDAsIHN1Z2dlc3Rpb246IDAsIHJlYWRpbmc6IDAsIHRyYWluaW5nOiAwIH0sDQogICAgICAgIHsgbmFtZTogJ+mprOazoicsIG1lZXRpbmc6IDQwMCwgcHJvcG9zYWw6IDAsIG9waW5pb246IDAsIHN1Z2dlc3Rpb246IDEyLCByZWFkaW5nOiAwLCB0cmFpbmluZzogMTUgfSwNCiAgICAgICAgeyBuYW1lOiAn546L546J5rCRJywgbWVldGluZzogNDkwLCBwcm9wb3NhbDogMSwgb3BpbmlvbjogMiwgc3VnZ2VzdGlvbjogMCwgcmVhZGluZzogNCwgdHJhaW5pbmc6IDI1IH0sDQogICAgICAgIHsgbmFtZTogJ+eOi+S/iuWunScsIG1lZXRpbmc6IDUwMCwgcHJvcG9zYWw6IDAsIG9waW5pb246IDQsIHN1Z2dlc3Rpb246IDEsIHJlYWRpbmc6IDUsIHRyYWluaW5nOiA2MCB9LA0KICAgICAgICB7IG5hbWU6ICfmnY7mmI4nLCBtZWV0aW5nOiAzMjAsIHByb3Bvc2FsOiA4LCBvcGluaW9uOiAxLCBzdWdnZXN0aW9uOiAzLCByZWFkaW5nOiAyLCB0cmFpbmluZzogMTggfSwNCiAgICAgICAgeyBuYW1lOiAn5byg5Y2OJywgbWVldGluZzogMjgwLCBwcm9wb3NhbDogNSwgb3BpbmlvbjogMCwgc3VnZ2VzdGlvbjogMiwgcmVhZGluZzogMSwgdHJhaW5pbmc6IDEyIH0sDQogICAgICAgIHsgbmFtZTogJ+WImOW8uicsIG1lZXRpbmc6IDQ1MCwgcHJvcG9zYWw6IDMsIG9waW5pb246IDYsIHN1Z2dlc3Rpb246IDAsIHJlYWRpbmc6IDMsIHRyYWluaW5nOiAzNSB9LA0KICAgICAgICB7IG5hbWU6ICfpmYjpnZknLCBtZWV0aW5nOiAzODAsIHByb3Bvc2FsOiAyLCBvcGluaW9uOiAzLCBzdWdnZXN0aW9uOiA0LCByZWFkaW5nOiA2LCB0cmFpbmluZzogMjggfQ0KICAgICAgXSwNCiAgICAgIC8vIOekvuaDheawkeaEj+aVsOaNrg0KICAgICAgc29jaWFsRGF0YTogew0KICAgICAgICBtZW1iZXJTdWJtaXQ6IHsNCiAgICAgICAgICBjb3VudDogMzQ1LA0KICAgICAgICAgIGFkb3B0ZWQ6IDIxDQogICAgICAgIH0sDQogICAgICAgIHVuaXRTdWJtaXQ6IHsNCiAgICAgICAgICBjb3VudDogNTQ3LA0KICAgICAgICAgIGFkb3B0ZWQ6IDc5DQogICAgICAgIH0sDQogICAgICAgIHRvdGFsOiAxMDU3DQogICAgICB9LA0KICAgICAgLy8g5Lya6K6u5rS75Yqo5pWw5o2uDQogICAgICBjb25mZXJlbmNlQWN0aXZpdGllc0RhdGE6IFsNCiAgICAgICAgeyBuYW1lOiAn5Lya6K6u5qyh5pWwJywgdmFsdWU6IDIwMSB9LA0KICAgICAgICB7IG5hbWU6ICfmtLvliqjmrKHmlbAnLCB2YWx1ZTogMzEwIH0sDQogICAgICAgIHsgbmFtZTogJ+S8muiuruS6uuaVsCcsIHZhbHVlOiAyNDEyIH0sDQogICAgICAgIHsgbmFtZTogJ+a0u+WKqOS6uuaVsCcsIHZhbHVlOiA0MDE1IH0NCiAgICAgIF0sDQogICAgICAvLyDnvZHnu5zorq7mlL/mlbDmja4NCiAgICAgIGRpc2N1c3Npb25zRGF0YTogew0KICAgICAgICBzdGF0aXN0aWNzOiBbDQogICAgICAgICAgeyBuYW1lOiAn5Y+R5biD6K6u6aKYJywgdmFsdWU6IDcyLCB1bml0OiAn5LiqJyB9LA0KICAgICAgICAgIHsgbmFtZTogJ+e0r+iuoeWPguS4juS6uuasoScsIHZhbHVlOiAzOTMwMSwgdW5pdDogJ+asoScgfSwNCiAgICAgICAgICB7IG5hbWU6ICfntK/orqHlvoHmsYLmhI/op4EnLCB2YWx1ZTogMTIzMDYsIHVuaXQ6ICfmnaEnIH0NCiAgICAgICAgXSwNCiAgICAgICAgaG90VG9waWNzOiBbDQogICAgICAgICAgJ+aOqOi/m+m7hOays+WbveWutuaWh+WMluWFrOWbreW7uuiuvicsDQogICAgICAgICAgJ+aMgee7reaOqOi/m+m7hOays+a1geWfn+eUn+aAgeS/neaKpOS/ruWkje+8jOWKqeWKmyLlhYjooYzljLoi5bu66K6+JywNCiAgICAgICAgICAn5YWo6Z2i5Yqg5by65paw5pe25Luj5Lit5bCP5a2m5Yqz5Yqo5pWZ6IKyJw0KICAgICAgICBdDQogICAgICB9LA0KICAgICAgbWFwRGF0YTogWw0KICAgICAgICB7IG5hbWU6ICfluILljZfljLonLCB2YWx1ZTogJzEyMDAnLCBhcmVhSWQ6ICczNzAyMDInLCBhZGNvZGU6IDM3MDIwMiB9LA0KICAgICAgICB7IG5hbWU6ICfluILljJfljLonLCB2YWx1ZTogJzEzMDAnLCBhcmVhSWQ6ICczNzAyMDMnLCBhZGNvZGU6IDM3MDIwMyB9LA0KICAgICAgICB7IG5hbWU6ICfpu4TlspvljLonLCB2YWx1ZTogJzg1MCcsIGFyZWFJZDogJzM3MDIxMScsIGFkY29kZTogMzcwMjExIH0sDQogICAgICAgIHsgbmFtZTogJ+W0guWxseWMuicsIHZhbHVlOiAnNzAwJywgYXJlYUlkOiAnMzcwMjEyJywgYWRjb2RlOiAzNzAyMTIgfSwNCiAgICAgICAgeyBuYW1lOiAn5p2O5rKn5Yy6JywgdmFsdWU6ICcxMDAwJywgYXJlYUlkOiAnMzcwMjEzJywgYWRjb2RlOiAzNzAyMTMgfSwNCiAgICAgICAgeyBuYW1lOiAn5Z+O6Ziz5Yy6JywgdmFsdWU6ICcxMTAwJywgYXJlYUlkOiAnMzcwMjE0JywgYWRjb2RlOiAzNzAyMTQgfSwNCiAgICAgICAgeyBuYW1lOiAn5Y2z5aKo5Yy6JywgdmFsdWU6ICc5NTAnLCBhcmVhSWQ6ICczNzAyMTUnLCBhZGNvZGU6IDM3MDIxNSB9LA0KICAgICAgICB7IG5hbWU6ICfog7blt57luIInLCB2YWx1ZTogJzgwMCcsIGFyZWFJZDogJzM3MDI4MScsIGFkY29kZTogMzcwMjgxIH0sDQogICAgICAgIHsgbmFtZTogJ+W5s+W6puW4gicsIHZhbHVlOiAnMTQwMCcsIGFyZWFJZDogJzM3MDI4MycsIGFkY29kZTogMzcwMjgzIH0sDQogICAgICAgIHsgbmFtZTogJ+iOseilv+W4gicsIHZhbHVlOiAnNjAwJywgYXJlYUlkOiAnMzcwMjg1JywgYWRjb2RlOiAzNzAyODUgfQ0KICAgICAgXSwNCiAgICAgIGFyZWFJZDogSlNPTi5wYXJzZShzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCdhcmVhSWQnICsgdGhpcy4kbG9nbygpKSksDQogICAgICBhcmVhTmFtZTogJ+mdkuWym+W4gicNCiAgICB9DQogIH0sDQogIGNvbXB1dGVkOiB7DQogIH0sDQogIG1vdW50ZWQgKCkgew0KICAgIHRoaXMuaW5pdFNjcmVlbigpDQogICAgdGhpcy51cGRhdGVUaW1lKCkNCiAgICB0aGlzLnRpbWVJbnRlcnZhbCA9IHNldEludGVydmFsKHRoaXMudXBkYXRlVGltZSwgMTAwMCkNCiAgfSwNCiAgYmVmb3JlRGVzdHJveSAoKSB7DQogICAgaWYgKHRoaXMudGltZUludGVydmFsKSB7DQogICAgICBjbGVhckludGVydmFsKHRoaXMudGltZUludGVydmFsKQ0KICAgIH0NCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGluaXRTY3JlZW4gKCkgew0KICAgICAgY29uc3QgeyBjYWxjUmF0ZSwgd2luZG93RHJhdyB9ID0gdXNlSW5kZXgodGhpcy4kcmVmcy5iaWdTY3JlZW4pDQogICAgICBjYWxjUmF0ZSgpDQogICAgICB3aW5kb3dEcmF3KCkNCiAgICB9LA0KICAgIHVwZGF0ZVRpbWUgKCkgew0KICAgICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKQ0KICAgICAgdGhpcy5jdXJyZW50VGltZSA9IG5vdy50b0xvY2FsZVN0cmluZygnemgtQ04nLCB7DQogICAgICAgIHllYXI6ICdudW1lcmljJywNCiAgICAgICAgbW9udGg6ICcyLWRpZ2l0JywNCiAgICAgICAgZGF5OiAnMi1kaWdpdCcsDQogICAgICAgIGhvdXI6ICcyLWRpZ2l0JywNCiAgICAgICAgbWludXRlOiAnMi1kaWdpdCcsDQogICAgICAgIHNlY29uZDogJzItZGlnaXQnDQogICAgICB9KQ0KICAgIH0sDQogICAgZ2V0SXRlbUNsYXNzIChuYW1lKSB7DQogICAgICBpZiAobmFtZS5pbmNsdWRlcygn5Lya6K6uJykpIHsNCiAgICAgICAgcmV0dXJuICdtZWV0aW5nLWl0ZW0nDQogICAgICB9IGVsc2UgaWYgKG5hbWUuaW5jbHVkZXMoJ+a0u+WKqCcpKSB7DQogICAgICAgIHJldHVybiAnYWN0aXZpdHktaXRlbS1iZycNCiAgICAgIH0NCiAgICAgIHJldHVybiAnJw0KICAgIH0sDQogICAgaGFuZGxlUmVnaW9uQ2xpY2sgKHJlZ2lvbikgew0KICAgICAgY29uc29sZS5sb2coJ+mAieS4reWcsOWMujonLCByZWdpb24pDQogICAgICAvLyDov5nph4zlj6/ku6Xmt7vliqDlnLDljLrngrnlh7vlkI7nmoTkuJrliqHpgLvovpENCiAgICAgIC8vIOavlOWmguaYvuekuuivpeWcsOWMuueahOivpue7huaVsOaNruetiQ0KICAgIH0sDQogICAgLy8g5omT5byA5aeU5ZGY57uf6K6hDQogICAgaGFuZGxlQ29tbWl0dGVlQ2xpY2sgKCkgew0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goeyBwYXRoOiAnL2NvbW1pdHRlZVN0YXRpc3RpY3NCb3gnLCBxdWVyeTogeyByb3V0ZTogJy9jb21taXR0ZWVTdGF0aXN0aWNzQm94JyB9IH0pDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["committeeStatisticsBox.vue"], "names": [], "mappings": ";AA0MA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "committeeStatisticsBox.vue", "sourceRoot": "src/views/smartBrainLargeScreen/committeeStatistics", "sourcesContent": ["<template>\r\n  <div class=\"big-screen\" ref=\"bigScreen\">\r\n    <div class=\"screen-header\">\r\n      <div class=\"header-left\">\r\n        <span class=\"date-time\">{{ currentTime }}</span>\r\n        <span class=\"weather\">晴 24℃ 东南风</span>\r\n      </div>\r\n      <div class=\"header-center\">\r\n        <img src=\"../../../assets/largeScreen/top_header_txt.png\" alt=\"\" style=\"height: 50px;\">\r\n      </div>\r\n      <div class=\"header-right\"></div>\r\n    </div>\r\n    <div class=\"screen-content\">\r\n      <div class=\"left-panel\">\r\n        <!-- 委员统计 -->\r\n        <div class=\"committee_statistics\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\" @click=\"handleCommitteeClick\">委员统计</span>\r\n            <span class=\"header_text_right\">十二届二次</span>\r\n          </div>\r\n          <div class=\"committee_statistics_content\">\r\n            <div class=\"committee_statistics_num\">\r\n              <div v-for=\"(item, index) in committeeStatisticsNum\" :key=\"index\" class=\"num_box\">\r\n                <img :src=\"item.icon\" alt=\"\" class=\"num_icon\">\r\n                <div>\r\n                  <div class=\"num_label\">{{ item.label }}</div>\r\n                  <div class=\"num_value\" :style=\"`color:${item.color}`\">{{ item.value }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"committee_statistics_chart\">\r\n              <BarScrollChart id=\"committee-statistics\" :chart-data=\"committeeBarData\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- 提案统计 -->\r\n        <div class=\"proposal_statistics\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">提案统计</span>\r\n            <span class=\"header_text_right\">十二届二次会议</span>\r\n            <span class=\"header_text_center\">提交提案总数：<span>873</span>件</span>\r\n          </div>\r\n          <div class=\"proposal_statistics_content\">\r\n            <div class=\"proposal_statistics_num\">\r\n              <div v-for=\"(item, index) in proposalStatisticsNum\" :key=\"index\" class=\"num_box\">\r\n                <img :src=\"item.icon\" alt=\"\" class=\"num_icon\">\r\n                <div>\r\n                  <div class=\"num_label\">{{ item.label }}</div>\r\n                  <div class=\"num_value\" :style=\"`color:${item.color}`\">{{ item.value }}<span class=\"num_unit\">{{\r\n                    item.unit }}</span></div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"proposal_statistics_chart\">\r\n              <PieChart id=\"proposal-statistics\" :chart-data=\"proposalChartData\" :name=\"proposalChartName\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- 工作动态 -->\r\n        <div class=\"work_dynamics\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">工作动态</span>\r\n            <span class=\"header_text_right\">本年</span>\r\n          </div>\r\n          <div class=\"work_dynamics_content\">\r\n            <div class=\"dynamics-list\">\r\n              <div v-for=\"(item, index) in workDynamicsData\" :key=\"item.id\" class=\"dynamics-item\"\r\n                :class=\"{ 'with-bg-image': index % 2 === 0, 'with-bg-color': index % 2 === 1 }\">\r\n                <div class=\"dynamics-content\">\r\n                  <div class=\"dynamics-title\">{{ item.title }}</div>\r\n                  <div class=\"dynamics-date\">{{ item.date }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"center-panel\">\r\n        <!-- 地图 -->\r\n        <div class=\"map_box\">\r\n          <MapComponent :data=\"mapData\" :areaId=\"areaId + ''\" :areaName=\"areaName\" @region-click=\"handleRegionClick\" />\r\n        </div>\r\n        <!-- 履职统计 -->\r\n        <div class=\"performance_statistics\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">履职统计</span>\r\n            <span class=\"header_text_right\">十二届二次</span>\r\n          </div>\r\n          <div class=\"performance_statistics_content\">\r\n            <div class=\"table-container\">\r\n              <!-- 固定表头 -->\r\n              <div class=\"table-header\">\r\n                <div class=\"header-cell\">姓名</div>\r\n                <div class=\"header-cell\">会议活动</div>\r\n                <div class=\"header-cell\">政协提案</div>\r\n                <div class=\"header-cell\">社情民意</div>\r\n                <div class=\"header-cell\">议政建言</div>\r\n                <div class=\"header-cell\">读书心得</div>\r\n                <div class=\"header-cell\">委员培训</div>\r\n                <div class=\"header-cell\"></div> <!-- 滚动条占位 -->\r\n              </div>\r\n              <!-- 可滚动内容 -->\r\n              <div class=\"table-body\">\r\n                <div class=\"table-row\" v-for=\"(item, index) in performanceData\" :key=\"index\">\r\n                  <div class=\"table-cell name-col\">{{ item.name }}</div>\r\n                  <div class=\"table-cell meeting-col\">{{ item.meeting }}</div>\r\n                  <div class=\"table-cell proposal-col\">{{ item.proposal }}</div>\r\n                  <div class=\"table-cell opinion-col\">{{ item.opinion }}</div>\r\n                  <div class=\"table-cell suggestion-col\">{{ item.suggestion }}\r\n                  </div>\r\n                  <div class=\"table-cell reading-col\">{{ item.reading }}</div>\r\n                  <div class=\"table-cell training-col\">{{ item.training }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"right-panel\">\r\n        <!-- 社情民意 -->\r\n        <div class=\"social\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">社情民意</span>\r\n            <span class=\"header_text_right\">本年</span>\r\n          </div>\r\n          <div class=\"social_content\">\r\n            <div class=\"social-data-container\">\r\n              <div class=\"left-data-item\">\r\n                <div class=\"left-data-label\">委员报送</div>\r\n                <div class=\"left-data-value\">总数<span>{{ socialData.memberSubmit.count }}</span>篇</div>\r\n                <div class=\"left-data-detail\">采用<span>{{ socialData.memberSubmit.adopted }}</span> 篇</div>\r\n              </div>\r\n              <div class=\"center-chart\">\r\n                <div class=\"progress-content\">\r\n                  <div class=\"total-number\">{{ socialData.total }}</div>\r\n                  <div class=\"total-label\">总数</div>\r\n                </div>\r\n              </div>\r\n              <div class=\"right-data-item\">\r\n                <div class=\"right-data-label\">单位报送</div>\r\n                <div class=\"right-data-value\">总数<span>{{ socialData.unitSubmit.count }}</span>篇</div>\r\n                <div class=\"right-data-detail\">采用<span>{{ socialData.unitSubmit.adopted }}</span> 篇</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- 会议活动 -->\r\n        <div class=\"conference_activities\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">会议活动</span>\r\n            <span class=\"header_text_right\">本年</span>\r\n          </div>\r\n          <div class=\"conference_activities_content\">\r\n            <div class=\"activities-grid\">\r\n              <div v-for=\"(item, index) in conferenceActivitiesData\" :key=\"index\" class=\"activity-item\"\r\n                :class=\"getItemClass(item.name)\">\r\n                <div class=\"activity-value\">{{ item.value }}</div>\r\n                <div class=\"activity-name\">{{ item.name }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- 网络议政 -->\r\n        <div class=\"discussions\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">网络议政</span>\r\n            <span class=\"header_text_right\"></span>\r\n          </div>\r\n          <div class=\"discussions_content\">\r\n            <!-- 统计数据区域 -->\r\n            <div class=\"statistics-section\">\r\n              <div v-for=\"(item, index) in discussionsData.statistics\" :key=\"index\" class=\"stat-item\">\r\n                <div class=\"stat-dot\"></div>\r\n                <div class=\"stat-info\">\r\n                  <span class=\"stat-name\">{{ item.name }}</span>\r\n                  <span class=\"stat-value\">{{ item.value }}</span>\r\n                  <span class=\"stat-unit\">{{ item.unit }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 最热话题区域 -->\r\n            <div class=\"hot-topics-section\">\r\n              <div class=\"hot-topics-header\">\r\n                <img src=\"../../../assets/largeScreen/icon_hot.png\" alt=\"热门\" class=\"hot-icon\">\r\n                <span class=\"hot-title\">最热话题</span>\r\n              </div>\r\n              <div class=\"topics-list\">\r\n                <div v-for=\"(topic, index) in discussionsData.hotTopics\" :key=\"index\" class=\"topic-item\">\r\n                  <div class=\"topic-dot\"></div>\r\n                  <span class=\"topic-text\">{{ topic }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { useIndex } from '../screen.js'\r\nimport MapComponent from '../components/MapComponent.vue'\r\nimport PieChart from '../components/PieChart.vue'\r\nimport BarScrollChart from '../components/BarScrollChart.vue'\r\n\r\nexport default {\r\n  name: 'BigScreen',\r\n  components: {\r\n    MapComponent,\r\n    PieChart,\r\n    BarScrollChart\r\n  },\r\n  data () {\r\n    return {\r\n      currentTime: '',\r\n      // 委员统计\r\n      committeeStatisticsNum: [\r\n        { icon: require('../../../assets/largeScreen/icon_cppcc_member.png'), label: '政协委员（人）', value: '10095', color: '#ffffff' },\r\n        { icon: require('../../../assets/largeScreen/icon_committee_member.png'), label: '政协常委', value: '8742', color: '#FCD603' }\r\n      ],\r\n      // 委员统计柱状图数据\r\n      committeeBarData: [\r\n        { name: '中共', value: 32 },\r\n        { name: '民革', value: 15 },\r\n        { name: '民盟', value: 14 },\r\n        { name: '民建', value: 13 },\r\n        { name: '民进', value: 12 },\r\n        { name: '农工', value: 10 },\r\n        { name: '致公', value: 8 },\r\n        { name: '九三', value: 7 },\r\n        { name: '台盟', value: 6 },\r\n        { name: '无党派', value: 5 }\r\n      ],\r\n      // 提案统计\r\n      proposalStatisticsNum: [\r\n        { icon: require('../../../assets/largeScreen/icon_committee_proposal.png'), label: '委员提案', value: '456', unit: '件', color: '#0DBCDB' },\r\n        { icon: require('../../../assets/largeScreen/icon_circles_proposal.png'), label: '界别提案', value: '354', unit: '件', color: '#0058FF' },\r\n        { icon: require('../../../assets/largeScreen/icon_committee_proposal.png'), label: '组织提案', value: '211', unit: '件', color: '#1AEBDD' }\r\n      ],\r\n      // 提案统计图表数据\r\n      proposalChartData: [\r\n        { name: '政府制约', value: 22.52 },\r\n        { name: '县区市政', value: 18.33 },\r\n        { name: '司法法治', value: 15.73 },\r\n        { name: '区市政府', value: 11.34 },\r\n        { name: '科技工商', value: 9.56 },\r\n        { name: '教育文化', value: 8.09 },\r\n        { name: '派出机构', value: 4.21 },\r\n        { name: '社会事业', value: 3.71 },\r\n        { name: '企事业', value: 3.65 },\r\n        { name: '农村卫生', value: 3.21 },\r\n        { name: '其他机构', value: 1.86 },\r\n        { name: '各群体他', value: 1.02 }\r\n      ],\r\n      proposalChartName: '提案统计',\r\n      // 工作动态数据\r\n      workDynamicsData: [\r\n        {\r\n          id: 1,\r\n          title: '市政协社会和法制工作办公室围绕\"居家适老化改造\"开展专题调研',\r\n          date: '2025-06-03'\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '\"与民同行 共创共赢\"新格局下民营企业转型发展座谈会召开',\r\n          date: '2025-05-30'\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '\"惠民生·基层行\"义诊活动温暖人心',\r\n          date: '2025-05-30'\r\n        },\r\n        {\r\n          id: 4,\r\n          title: '市科技局面复市政协科技界别提案',\r\n          date: '2025-05-30'\r\n        },\r\n        {\r\n          id: 5,\r\n          title: '市政协召开\"推进数字化转型\"专题协商会',\r\n          date: '2025-05-28'\r\n        },\r\n        {\r\n          id: 6,\r\n          title: '政协委员深入基层开展\"三服务\"活动',\r\n          date: '2025-05-25'\r\n        }\r\n      ],\r\n      // 履职统计数据\r\n      performanceData: [\r\n        { name: '马平安', meeting: 515, proposal: 15, opinion: 0, suggestion: 0, reading: 0, training: 0 },\r\n        { name: '马波', meeting: 400, proposal: 0, opinion: 0, suggestion: 12, reading: 0, training: 15 },\r\n        { name: '王玉民', meeting: 490, proposal: 1, opinion: 2, suggestion: 0, reading: 4, training: 25 },\r\n        { name: '王俊宝', meeting: 500, proposal: 0, opinion: 4, suggestion: 1, reading: 5, training: 60 },\r\n        { name: '李明', meeting: 320, proposal: 8, opinion: 1, suggestion: 3, reading: 2, training: 18 },\r\n        { name: '张华', meeting: 280, proposal: 5, opinion: 0, suggestion: 2, reading: 1, training: 12 },\r\n        { name: '刘强', meeting: 450, proposal: 3, opinion: 6, suggestion: 0, reading: 3, training: 35 },\r\n        { name: '陈静', meeting: 380, proposal: 2, opinion: 3, suggestion: 4, reading: 6, training: 28 }\r\n      ],\r\n      // 社情民意数据\r\n      socialData: {\r\n        memberSubmit: {\r\n          count: 345,\r\n          adopted: 21\r\n        },\r\n        unitSubmit: {\r\n          count: 547,\r\n          adopted: 79\r\n        },\r\n        total: 1057\r\n      },\r\n      // 会议活动数据\r\n      conferenceActivitiesData: [\r\n        { name: '会议次数', value: 201 },\r\n        { name: '活动次数', value: 310 },\r\n        { name: '会议人数', value: 2412 },\r\n        { name: '活动人数', value: 4015 }\r\n      ],\r\n      // 网络议政数据\r\n      discussionsData: {\r\n        statistics: [\r\n          { name: '发布议题', value: 72, unit: '个' },\r\n          { name: '累计参与人次', value: 39301, unit: '次' },\r\n          { name: '累计征求意见', value: 12306, unit: '条' }\r\n        ],\r\n        hotTopics: [\r\n          '推进黄河国家文化公园建设',\r\n          '持续推进黄河流域生态保护修复，助力\"先行区\"建设',\r\n          '全面加强新时代中小学劳动教育'\r\n        ]\r\n      },\r\n      mapData: [\r\n        { name: '市南区', value: '1200', areaId: '370202', adcode: 370202 },\r\n        { name: '市北区', value: '1300', areaId: '370203', adcode: 370203 },\r\n        { name: '黄岛区', value: '850', areaId: '370211', adcode: 370211 },\r\n        { name: '崂山区', value: '700', areaId: '370212', adcode: 370212 },\r\n        { name: '李沧区', value: '1000', areaId: '370213', adcode: 370213 },\r\n        { name: '城阳区', value: '1100', areaId: '370214', adcode: 370214 },\r\n        { name: '即墨区', value: '950', areaId: '370215', adcode: 370215 },\r\n        { name: '胶州市', value: '800', areaId: '370281', adcode: 370281 },\r\n        { name: '平度市', value: '1400', areaId: '370283', adcode: 370283 },\r\n        { name: '莱西市', value: '600', areaId: '370285', adcode: 370285 }\r\n      ],\r\n      areaId: JSON.parse(sessionStorage.getItem('areaId' + this.$logo())),\r\n      areaName: '青岛市'\r\n    }\r\n  },\r\n  computed: {\r\n  },\r\n  mounted () {\r\n    this.initScreen()\r\n    this.updateTime()\r\n    this.timeInterval = setInterval(this.updateTime, 1000)\r\n  },\r\n  beforeDestroy () {\r\n    if (this.timeInterval) {\r\n      clearInterval(this.timeInterval)\r\n    }\r\n  },\r\n  methods: {\r\n    initScreen () {\r\n      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)\r\n      calcRate()\r\n      windowDraw()\r\n    },\r\n    updateTime () {\r\n      const now = new Date()\r\n      this.currentTime = now.toLocaleString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit',\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        second: '2-digit'\r\n      })\r\n    },\r\n    getItemClass (name) {\r\n      if (name.includes('会议')) {\r\n        return 'meeting-item'\r\n      } else if (name.includes('活动')) {\r\n        return 'activity-item-bg'\r\n      }\r\n      return ''\r\n    },\r\n    handleRegionClick (region) {\r\n      console.log('选中地区:', region)\r\n      // 这里可以添加地区点击后的业务逻辑\r\n      // 比如显示该地区的详细数据等\r\n    },\r\n    // 打开委员统计\r\n    handleCommitteeClick () {\r\n      this.$router.push({ path: '/committeeStatisticsBox', query: { route: '/committeeStatisticsBox' } })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.big-screen {\r\n  width: 1920px;\r\n  height: 1080px;\r\n  position: relative;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  transform-origin: left top;\r\n  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;\r\n  background-size: cover;\r\n  background-position: center;\r\n\r\n  .screen-header {\r\n    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;\r\n    background-size: 100% 100%;\r\n    background-position: center;\r\n    height: 65px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 40px;\r\n\r\n    .header-left {\r\n      display: flex;\r\n      gap: 20px;\r\n      font-size: 14px;\r\n      color: #8cc8ff;\r\n      flex: 1;\r\n    }\r\n\r\n    .header-center {\r\n      width: 60%;\r\n      text-align: center;\r\n    }\r\n\r\n    .header-right {\r\n      flex: 1;\r\n    }\r\n  }\r\n\r\n  .screen-content {\r\n    height: calc(100% - 65px);\r\n    display: flex;\r\n    padding: 35px 20px 0 20px;\r\n    gap: 30px;\r\n\r\n    .header_box {\r\n      position: absolute;\r\n      top: 15px;\r\n      left: 24px;\r\n      right: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n\r\n      .header_text_left {\r\n        font-weight: bold;\r\n        font-size: 20px;\r\n        color: #FFFFFF;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .header_text_right {\r\n        font-size: 15px;\r\n        color: #FFD600;\r\n      }\r\n\r\n      .header_text_center {\r\n        font-size: 15px;\r\n        color: #FFFFFF;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          font-weight: 500;\r\n          font-size: 24px;\r\n          color: #02FBFB;\r\n          margin: 0 10px 0 6px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .left-panel,\r\n    .right-panel {\r\n      width: 470px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 20px 30px;\r\n    }\r\n\r\n    .left-panel {\r\n      .committee_statistics {\r\n        background: url('../../../assets/largeScreen/committee_statistics_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 320px;\r\n        width: 100%;\r\n\r\n        .committee_statistics_content {\r\n          height: 100%;\r\n          margin-top: 72px;\r\n          margin-left: 20px;\r\n          margin-right: 20px;\r\n\r\n          .committee_statistics_num {\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-around;\r\n\r\n            .num_box {\r\n              display: flex;\r\n              align-items: center;\r\n\r\n              .num_icon {\r\n                width: 64px;\r\n                height: 64px;\r\n                margin-right: 14px;\r\n              }\r\n\r\n              .num_label {\r\n                font-size: 15px;\r\n                color: #B4C0CC;\r\n                margin-bottom: 14px;\r\n              }\r\n\r\n              .num_value {\r\n                font-weight: bold;\r\n                font-size: 26px;\r\n                color: #FFFFFF;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .proposal_statistics {\r\n        background: url('../../../assets/largeScreen/proposal_statistics_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 310px;\r\n        width: 100%;\r\n\r\n        .proposal_statistics_content {\r\n          height: 100%;\r\n          margin-top: 72px;\r\n          margin-left: 20px;\r\n          margin-right: 20px;\r\n\r\n          .proposal_statistics_num {\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n\r\n            .num_box {\r\n              display: flex;\r\n              align-items: center;\r\n\r\n              .num_icon {\r\n                width: 54px;\r\n                height: 54px;\r\n                margin-right: 10px;\r\n              }\r\n\r\n              .num_label {\r\n                font-size: 14px;\r\n                color: #FFFFFF;\r\n                margin-bottom: 5px;\r\n              }\r\n\r\n              .num_value {\r\n                font-size: 20px;\r\n                color: #0DBCDB;\r\n                font-weight: 500;\r\n\r\n                .num_unit {\r\n                  font-size: 14px;\r\n                  color: #FFFFFF;\r\n                  font-weight: normal;\r\n                  margin-left: 4px;\r\n                }\r\n              }\r\n            }\r\n          }\r\n\r\n          .proposal_statistics_chart {\r\n            height: 180px;\r\n            margin-top: 20px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .work_dynamics {\r\n        background: url('../../../assets/largeScreen/work_dynamics_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 270px;\r\n        width: 100%;\r\n\r\n        .work_dynamics_content {\r\n          height: 100%;\r\n          margin-top: 65px;\r\n          margin-left: 14px;\r\n          margin-right: 14px;\r\n\r\n          .dynamics-list {\r\n            height: calc(100% - 70px);\r\n            overflow-y: auto;\r\n\r\n            &::-webkit-scrollbar {\r\n              width: 4px;\r\n            }\r\n\r\n            &::-webkit-scrollbar-track {\r\n              background: rgba(0, 30, 60, 0.3);\r\n              border-radius: 2px;\r\n            }\r\n\r\n            &::-webkit-scrollbar-thumb {\r\n              background: rgba(0, 212, 255, 0.4);\r\n              border-radius: 2px;\r\n\r\n              &:hover {\r\n                background: rgba(0, 212, 255, 0.6);\r\n              }\r\n            }\r\n\r\n            .dynamics-item {\r\n              margin-bottom: 12px;\r\n              overflow: hidden;\r\n              position: relative;\r\n\r\n              &:last-child {\r\n                margin-bottom: 0;\r\n              }\r\n\r\n              // 奇数项 - 背景图片样式\r\n              &.with-bg-image {\r\n                background: url('../../../assets/largeScreen/table_bg.png') no-repeat;\r\n                background-size: 100% 100%;\r\n                background-position: center;\r\n              }\r\n\r\n              // 偶数项 - 背景颜色样式\r\n              &.with-bg-color {\r\n                background: rgba(6, 79, 219, 0.05);\r\n              }\r\n\r\n              .dynamics-content {\r\n                padding: 12px 15px;\r\n                position: relative;\r\n                z-index: 2;\r\n                display: flex;\r\n                justify-content: space-between;\r\n                align-items: center;\r\n\r\n                .dynamics-title {\r\n                  flex: 1;\r\n                  color: #fff;\r\n                  font-size: 16px;\r\n                  margin-right: 16px;\r\n                  // 文本溢出处理\r\n                  display: -webkit-box;\r\n                  -webkit-line-clamp: 1;\r\n                  -webkit-box-orient: vertical;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                }\r\n\r\n                .dynamics-date {\r\n                  flex-shrink: 0;\r\n                  font-size: 16px;\r\n                  color: #FFFFFF;\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .right-panel {\r\n      .social {\r\n        background: url('../../../assets/largeScreen/social_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 290px;\r\n        width: 100%;\r\n\r\n        .social_content {\r\n          height: 190px;\r\n          margin-top: 75px;\r\n          margin-left: 12px;\r\n          margin-right: 12px;\r\n          background: url('../../../assets/largeScreen/social_content_bg.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n\r\n          .social-data-container {\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n            width: 100%;\r\n            height: 100%;\r\n\r\n            .left-data-item {\r\n              display: flex;\r\n              flex-direction: column;\r\n              align-items: center;\r\n              text-align: center;\r\n              flex: 1;\r\n              margin-right: 20px;\r\n\r\n              .left-data-label {\r\n                font-size: 14px;\r\n                color: #19ECFF;\r\n                margin-bottom: 20px;\r\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\r\n              }\r\n\r\n              .left-data-value {\r\n                font-size: 14px;\r\n                color: #FFFFFF;\r\n                margin-bottom: 15px;\r\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\r\n\r\n                span {\r\n                  font-weight: 400;\r\n                  font-size: 20px;\r\n                  color: #FFD600;\r\n                  margin: 0 5px;\r\n                }\r\n              }\r\n\r\n              .left-data-detail {\r\n                font-size: 14px;\r\n                color: #FFFFFF;\r\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\r\n\r\n                span {\r\n                  font-weight: 400;\r\n                  font-size: 20px;\r\n                  color: #19ECFF;\r\n                  margin: 0 5px;\r\n                }\r\n              }\r\n            }\r\n\r\n            .center-chart {\r\n              flex: 1;\r\n              display: flex;\r\n              justify-content: center;\r\n              align-items: center;\r\n\r\n              .progress-content {\r\n                position: absolute;\r\n                display: flex;\r\n                flex-direction: column;\r\n                align-items: center;\r\n                justify-content: center;\r\n\r\n                .total-number {\r\n                  font-weight: 500;\r\n                  font-size: 24px;\r\n                  color: #FFD600;\r\n                  margin-bottom: 8px;\r\n                }\r\n\r\n                .total-label {\r\n                  font-size: 14px;\r\n                  color: #ffffff;\r\n                }\r\n              }\r\n            }\r\n\r\n            .right-data-item {\r\n              display: flex;\r\n              flex-direction: column;\r\n              align-items: center;\r\n              text-align: center;\r\n              flex: 1;\r\n              margin-left: 20px;\r\n\r\n              .right-data-label {\r\n                font-size: 14px;\r\n                color: #19ECFF;\r\n                margin-bottom: 20px;\r\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\r\n              }\r\n\r\n              .right-data-value {\r\n                font-size: 14px;\r\n                color: #FFFFFF;\r\n                margin-bottom: 15px;\r\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\r\n\r\n                span {\r\n                  font-weight: 400;\r\n                  font-size: 20px;\r\n                  color: #FFD600;\r\n                  margin: 0 5px;\r\n                }\r\n              }\r\n\r\n              .right-data-detail {\r\n                font-size: 14px;\r\n                color: #FFFFFF;\r\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\r\n\r\n                span {\r\n                  font-weight: 400;\r\n                  font-size: 20px;\r\n                  color: #19ECFF;\r\n                  margin: 0 5px;\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .conference_activities {\r\n        background: url('../../../assets/largeScreen/conference_activities_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 290px;\r\n        width: 100%;\r\n\r\n        .conference_activities_content {\r\n          margin-top: 70px;\r\n          margin-left: 20px;\r\n          margin-right: 20px;\r\n\r\n          .activities-grid {\r\n            display: grid;\r\n            grid-template-columns: 1fr 1fr;\r\n            grid-template-rows: 1fr 1fr;\r\n            gap: 15px;\r\n            height: 100%;\r\n\r\n            .activity-item {\r\n              display: flex;\r\n              flex-direction: column;\r\n              align-items: center;\r\n              justify-content: center;\r\n              background-size: 100% 100%;\r\n              background-repeat: no-repeat;\r\n              background-position: center;\r\n              height: 92px;\r\n\r\n              .activity-value {\r\n                font-weight: 500;\r\n                font-size: 32px;\r\n                color: #FFFFFF;\r\n                line-height: 24px;\r\n                margin-bottom: 12px;\r\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\r\n              }\r\n\r\n              .activity-name {\r\n                font-size: 16px;\r\n                color: #FFFFFF;\r\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\r\n              }\r\n\r\n              &.meeting-item {\r\n                background-image: url('../../../assets/largeScreen/icon_meeting_item_bg.png');\r\n              }\r\n\r\n              &.activity-item-bg {\r\n                background-image: url('../../../assets/largeScreen/icon_activity_item_bg.png');\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .discussions {\r\n        background: url('../../../assets/largeScreen/discussions_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 320px;\r\n        width: 100%;\r\n\r\n        .discussions_content {\r\n          margin-top: 75px;\r\n          margin-left: 20px;\r\n          margin-right: 20px;\r\n          height: calc(100% - 90px);\r\n          display: flex;\r\n          flex-direction: column;\r\n\r\n          .statistics-section {\r\n            display: flex;\r\n            flex-wrap: wrap;\r\n            justify-content: space-between;\r\n            gap: 20px;\r\n            margin-bottom: 25px;\r\n\r\n            .stat-item {\r\n              display: flex;\r\n              align-items: center;\r\n              gap: 12px;\r\n\r\n              .stat-dot {\r\n                width: 10px;\r\n                height: 10px;\r\n                border-radius: 50%;\r\n                background: linear-gradient(180deg, #00EEFF 0%, #E1FDFF 100%);\r\n                flex-shrink: 0;\r\n                margin-top: 5px;\r\n              }\r\n\r\n              .stat-info {\r\n                display: flex;\r\n                align-items: center;\r\n                gap: 8px;\r\n\r\n                .stat-name {\r\n                  font-size: 15px;\r\n                  color: #FFFFFF;\r\n                  font-family: DIN-BoldItalic, DIN-BoldItalic;\r\n                }\r\n\r\n                .stat-value {\r\n                  font-weight: 500;\r\n                  font-size: 20px;\r\n                  color: #FFD600;\r\n                  font-family: DIN-BoldItalic, DIN-BoldItalic;\r\n                }\r\n\r\n                .stat-unit {\r\n                  font-size: 15px;\r\n                  color: #FFFFFF;\r\n                  font-family: DIN-BoldItalic, DIN-BoldItalic;\r\n                }\r\n              }\r\n            }\r\n          }\r\n\r\n          .hot-topics-section {\r\n            flex: 1;\r\n            background: rgba(31, 198, 255, 0.16);\r\n            padding: 12px 16px;\r\n\r\n            .hot-topics-header {\r\n              display: flex;\r\n              align-items: center;\r\n              gap: 8px;\r\n              margin-bottom: 15px;\r\n\r\n              .hot-icon {\r\n                width: 20px;\r\n                height: 20px;\r\n              }\r\n\r\n              .hot-title {\r\n                font-size: 16px;\r\n                color: #02FBFB;\r\n                font-weight: 500;\r\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\r\n              }\r\n            }\r\n\r\n            .topics-list {\r\n              display: flex;\r\n              flex-direction: column;\r\n              gap: 12px;\r\n\r\n              .topic-item {\r\n                display: flex;\r\n                align-items: flex-start;\r\n                gap: 10px;\r\n\r\n                .topic-dot {\r\n                  width: 6px;\r\n                  height: 6px;\r\n                  border-radius: 50%;\r\n                  background: rgba(217, 217, 217, 0.5);\r\n                  margin-top: 8px;\r\n                  flex-shrink: 0;\r\n                }\r\n\r\n                .topic-text {\r\n                  font-size: 14px;\r\n                  color: #FFFFFF;\r\n                  line-height: 22px;\r\n                  font-family: DIN-BoldItalic, DIN-BoldItalic;\r\n                  text-overflow: ellipsis;\r\n                  overflow: hidden;\r\n                  white-space: nowrap;\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .center-panel {\r\n      flex: 1;\r\n      gap: 20px;\r\n      display: flex;\r\n      flex-direction: column;\r\n\r\n      .map_box {\r\n        background: url('../../../assets/largeScreen/map_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        height: 650px;\r\n        width: 100%;\r\n        position: relative;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n      }\r\n\r\n      .performance_statistics {\r\n        background: url('../../../assets/largeScreen/performance_statistics_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 270px;\r\n        width: 100%;\r\n\r\n        .performance_statistics_content {\r\n          height: 100%;\r\n          margin-top: 65px;\r\n          margin-left: 16px;\r\n          margin-right: 16px;\r\n\r\n          .table-container {\r\n            height: calc(100% - 75px);\r\n            display: flex;\r\n            flex-direction: column;\r\n            border: 1px solid rgba(0, 212, 255, 0.2);\r\n            overflow: hidden;\r\n            /* 使用CSS Grid确保列对齐 */\r\n            --name-col-width: 120px;\r\n            --scrollbar-width: 6px;\r\n\r\n            .table-header {\r\n              display: grid;\r\n              grid-template-columns: var(--name-col-width) repeat(6, 1fr) var(--scrollbar-width);\r\n              border-bottom: 1px solid rgba(0, 212, 255, 0.4);\r\n              position: sticky;\r\n              top: 0;\r\n              z-index: 10;\r\n\r\n              .header-cell {\r\n                padding: 12px 8px;\r\n                text-align: center;\r\n                color: #E6F7FF;\r\n                font-size: 15px;\r\n                border-right: 1px solid rgba(0, 212, 255, 0.3);\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n\r\n                &:last-child {\r\n                  border-right: none;\r\n                  background: transparent;\r\n                  border: none;\r\n                }\r\n\r\n                // &.name-col {\r\n                //   background: rgba(0, 100, 180, 0.9);\r\n                //   font-weight: 600;\r\n                // }\r\n              }\r\n            }\r\n\r\n            .table-body {\r\n              flex: 1;\r\n              overflow-y: auto;\r\n\r\n              &::-webkit-scrollbar {\r\n                width: 6px;\r\n              }\r\n\r\n              &::-webkit-scrollbar-track {\r\n                background: rgba(0, 30, 60, 0.3);\r\n                border-radius: 3px;\r\n              }\r\n\r\n              &::-webkit-scrollbar-thumb {\r\n                background: rgba(0, 212, 255, 0.4);\r\n                border-radius: 3px;\r\n\r\n                &:hover {\r\n                  background: rgba(0, 212, 255, 0.6);\r\n                }\r\n              }\r\n\r\n              .table-row {\r\n                display: grid;\r\n                grid-template-columns: var(--name-col-width) repeat(6, 1fr);\r\n                border-bottom: 1px solid rgba(0, 212, 255, 0.4);\r\n                transition: all 0.3s ease;\r\n\r\n                &:hover {\r\n                  background: rgba(0, 212, 255, 0.1);\r\n                }\r\n\r\n                .table-cell {\r\n                  padding: 12px 8px;\r\n                  text-align: center;\r\n                  color: #FFFFFF;\r\n                  font-size: 14px;\r\n                  border-right: 1px solid rgba(0, 212, 255, 0.4);\r\n                  transition: all 0.3s ease;\r\n                  display: flex;\r\n                  align-items: center;\r\n                  justify-content: center;\r\n\r\n                  &:last-child {\r\n                    border-right: none;\r\n                  }\r\n\r\n                  &.name-col {\r\n                    background: rgba(0, 60, 120, 0.4);\r\n                    color: #FFF;\r\n                    font-weight: 500;\r\n                  }\r\n\r\n                  &.meeting-col {\r\n                    background: rgba(10, 63, 111, 0.4);\r\n                    color: #59F7CA;\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                  }\r\n\r\n                  &.proposal-col {\r\n                    background: rgba(10, 63, 111, 0.4);\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    color: #00FFF7;\r\n                  }\r\n\r\n                  &.opinion-col {\r\n                    background: rgba(10, 63, 111, 0.4);\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    color: #FF386B;\r\n                  }\r\n\r\n                  &.suggestion-col {\r\n                    background: rgba(10, 63, 111, 0.4);\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    color: #81C4E4;\r\n                  }\r\n\r\n                  &.reading-col {\r\n                    background: rgba(10, 63, 111, 0.4);\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    color: #387BFD;\r\n                  }\r\n\r\n                  &.training-col {\r\n                    background: rgba(10, 63, 111, 0.4);\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    color: #FF911F;\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}